package com.example.ttsandroiddemo.ui.utils

import android.content.ClipboardManager
import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext

/**
 * 文本输入工具类
 */
object TextInputUtils {
    
    /**
     * 示例文本列表
     */
    val sampleTexts = listOf(
        "欢迎使用TTS文本转语音功能！这是一个简单的示例文本。",
        "人工智能技术正在快速发展，为我们的生活带来了许多便利。",
        "春天来了，万物复苏，大地一片生机勃勃的景象。",
        "学习新技能需要持续的努力和实践，但收获是值得的。",
        "科技改变生活，创新驱动未来。让我们一起拥抱数字化时代。",
        "Hello, this is an English sample text for testing TTS functionality.",
        "Technology is advancing rapidly, bringing convenience to our daily lives.",
        "The quick brown fox jumps over the lazy dog. This sentence contains all letters of the alphabet."
    )
    
    /**
     * 从剪贴板获取文本
     */
    fun getTextFromClipboard(context: Context): String? {
        return try {
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = clipboardManager.primaryClip
            if (clipData != null && clipData.itemCount > 0) {
                clipData.getItemAt(0).text?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 复制文本到剪贴板
     */
    fun copyTextToClipboard(context: Context, text: String, label: String = "TTS Text") {
        try {
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = android.content.ClipData.newPlainText(label, text)
            clipboardManager.setPrimaryClip(clipData)
        } catch (e: Exception) {
            // 忽略错误
        }
    }
    
    /**
     * 获取随机示例文本
     */
    fun getRandomSampleText(): String {
        return sampleTexts.random()
    }
    
    /**
     * 验证文本长度
     */
    fun isTextLengthValid(text: String, maxLength: Int = 5000): Boolean {
        return text.length <= maxLength
    }
    
    /**
     * 清理文本（移除多余空格和换行）
     */
    fun cleanText(text: String): String {
        return text.trim()
            .replace(Regex("\\s+"), " ")
            .replace(Regex("\\n+"), "\n")
    }
    
    /**
     * 获取文本统计信息
     */
    fun getTextStatistics(text: String): TextStatistics {
        val cleanedText = text.trim()
        return TextStatistics(
            characterCount = text.length,
            wordCount = if (cleanedText.isEmpty()) 0 else cleanedText.split(Regex("\\s+")).size,
            lineCount = if (text.isEmpty()) 0 else text.lines().size,
            paragraphCount = if (cleanedText.isEmpty()) 0 else cleanedText.split(Regex("\\n\\s*\\n")).size
        )
    }
}

/**
 * 文本统计信息数据类
 */
data class TextStatistics(
    val characterCount: Int,
    val wordCount: Int,
    val lineCount: Int,
    val paragraphCount: Int
)

/**
 * Compose工具函数：获取剪贴板文本
 */
@Composable
fun rememberClipboardText(): String? {
    val context = LocalContext.current
    return remember {
        TextInputUtils.getTextFromClipboard(context)
    }
}