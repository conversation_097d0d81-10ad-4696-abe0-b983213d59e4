package com.example.ttsandroiddemo.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.ttsandroiddemo.ui.theme.TTSAndroidDemoTheme
import com.example.ttsandroiddemo.ui.utils.TextInputUtils
import com.example.ttsandroiddemo.ui.utils.TextStatistics

/**
 * 增强版文本输入组件，包含更多功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedTextInput(
    inputText: String,
    isTextValid: Boolean,
    errorMessage: String?,
    onTextChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    showStatistics: Boolean = true,
    showActions: Boolean = true
) {
    val context = LocalContext.current
    var showSampleDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "文本输入",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                
                // 状态指示器
                TextStatusIndicator(
                    isValid = isTextValid,
                    isEmpty = inputText.isEmpty()
                )
            }
            
            // 文本输入区域
            TextInputSection(
                inputText = inputText,
                isTextValid = isTextValid,
                errorMessage = errorMessage,
                onTextChange = onTextChange,
                onClearText = { onTextChange("") },
                enabled = enabled
            )
            
            // 快捷操作按钮
            AnimatedVisibility(
                visible = showActions,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                TextInputActions(
                    onPasteFromClipboard = {
                        val clipboardText = TextInputUtils.getTextFromClipboard(context)
                        clipboardText?.let { onTextChange(it) }
                    },
                    onClearText = { onTextChange("") },
                    onSelectSampleText = { showSampleDialog = true },
                    enabled = enabled
                )
            }
            
            // 文本统计信息
            AnimatedVisibility(
                visible = showStatistics && inputText.isNotEmpty(),
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                EnhancedTextStatistics(
                    text = inputText,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
    
    // 示例文本选择对话框
    if (showSampleDialog) {
        SampleTextDialog(
            onTextSelected = { selectedText ->
                onTextChange(selectedText)
                showSampleDialog = false
            },
            onDismiss = { showSampleDialog = false }
        )
    }
}

/**
 * 文本状态指示器
 */
@Composable
private fun TextStatusIndicator(
    isValid: Boolean,
    isEmpty: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = when {
                isEmpty -> Icons.Default.Edit
                isValid -> Icons.Default.CheckCircle
                else -> Icons.Default.Error
            },
            contentDescription = null,
            tint = when {
                isEmpty -> MaterialTheme.colorScheme.onSurfaceVariant
                isValid -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.error
            },
            modifier = Modifier.size(16.dp)
        )
        
        Text(
            text = when {
                isEmpty -> "待输入"
                isValid -> "就绪"
                else -> "错误"
            },
            style = MaterialTheme.typography.bodySmall,
            color = when {
                isEmpty -> MaterialTheme.colorScheme.onSurfaceVariant
                isValid -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.error
            }
        )
    }
}

/**
 * 增强版文本统计信息
 */
@Composable
private fun EnhancedTextStatistics(
    text: String,
    modifier: Modifier = Modifier
) {
    val statistics = remember(text) {
        TextInputUtils.getTextStatistics(text)
    }
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticItem(
                icon = Icons.Default.TextFields,
                label = "字符",
                value = statistics.characterCount.toString(),
                color = MaterialTheme.colorScheme.primary
            )
            
            StatisticItem(
                icon = Icons.Default.Article,
                label = "词语",
                value = statistics.wordCount.toString(),
                color = MaterialTheme.colorScheme.secondary
            )
            
            StatisticItem(
                icon = Icons.Default.FormatListNumbered,
                label = "行数",
                value = statistics.lineCount.toString(),
                color = MaterialTheme.colorScheme.tertiary
            )
            
            StatisticItem(
                icon = Icons.Default.ViewHeadline,
                label = "段落",
                value = statistics.paragraphCount.toString(),
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}

/**
 * 统计项组件（带图标）
 */
@Composable
private fun StatisticItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    color: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(16.dp)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            color = color,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 示例文本选择对话框
 */
@Composable
private fun SampleTextDialog(
    onTextSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("选择示例文本")
        },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(TextInputUtils.sampleTexts.size) { index ->
                    val sampleText = TextInputUtils.sampleTexts[index]
                    
                    Card(
                        onClick = { onTextSelected(sampleText) },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        SelectionContainer {
                            Text(
                                text = sampleText,
                                modifier = Modifier.padding(12.dp),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
private fun EnhancedTextInputPreview() {
    TTSAndroidDemoTheme {
        var text by remember { mutableStateOf("这是一个示例文本，用于测试TTS功能。\n\n这是第二段文本。") }
        
        EnhancedTextInput(
            inputText = text,
            isTextValid = true,
            errorMessage = null,
            onTextChange = { text = it },
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun EnhancedTextInputEmptyPreview() {
    TTSAndroidDemoTheme {
        EnhancedTextInput(
            inputText = "",
            isTextValid = false,
            errorMessage = "请输入要朗读的文本",
            onTextChange = { },
            modifier = Modifier.padding(16.dp)
        )
    }
}