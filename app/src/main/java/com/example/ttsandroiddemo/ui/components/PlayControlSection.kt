package com.example.ttsandroiddemo.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.ttsandroiddemo.ui.theme.TTSAndroidDemoTheme

/**
 * 播放控制区域组件
 */
@Composable
fun PlayControlSection(
    canPlay: Boolean,
    canStop: Boolean,
    isPlaying: Boolean,
    isInitialized: Boolean,
    onPlayClick: () -> Unit,
    onStopClick: () -> Unit,
    modifier: Modifier = Modifier,
    showStatus: Boolean = true
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 状态显示
            if (showStatus) {
                PlaybackStatus(
                    isInitialized = isInitialized,
                    isPlaying = isPlaying,
                    canPlay = canPlay
                )
            }
            
            // 主要控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 播放按钮
                PlayButton(
                    canPlay = canPlay,
                    isPlaying = isPlaying,
                    onClick = onPlayClick,
                    modifier = Modifier.size(72.dp)
                )
                
                // 停止按钮
                StopButton(
                    canStop = canStop,
                    onClick = onStopClick,
                    modifier = Modifier.size(56.dp)
                )
            }
            
            // 播放提示文本
            PlaybackHint(
                canPlay = canPlay,
                isPlaying = isPlaying,
                isInitialized = isInitialized
            )
        }
    }
}

/**
 * 播放状态指示器
 */
@Composable
private fun PlaybackStatus(
    isInitialized: Boolean,
    isPlaying: Boolean,
    canPlay: Boolean,
    modifier: Modifier = Modifier
) {
    val statusText = when {
        !isInitialized -> "TTS引擎初始化中..."
        isPlaying -> "正在播放"
        canPlay -> "准备就绪"
        else -> "等待输入文本"
    }
    
    val statusColor = when {
        !isInitialized -> MaterialTheme.colorScheme.outline
        isPlaying -> MaterialTheme.colorScheme.primary
        canPlay -> MaterialTheme.colorScheme.secondary
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    val statusIcon = when {
        !isInitialized -> Icons.Default.Sync
        isPlaying -> Icons.Default.VolumeUp
        canPlay -> Icons.Default.CheckCircle
        else -> Icons.Default.Edit
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 旋转动画（仅在初始化时）
        val rotation by animateFloatAsState(
            targetValue = if (!isInitialized) 360f else 0f,
            animationSpec = infiniteRepeatable(
                animation = tween(1000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "rotation"
        )
        
        Icon(
            imageVector = statusIcon,
            contentDescription = null,
            tint = statusColor,
            modifier = Modifier
                .size(20.dp)
                .rotate(if (!isInitialized) rotation else 0f)
        )
        
        Text(
            text = statusText,
            style = MaterialTheme.typography.bodyMedium,
            color = statusColor,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 播放按钮组件
 */
@Composable
private fun PlayButton(
    canPlay: Boolean,
    isPlaying: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 按钮颜色动画
    val buttonColors = ButtonDefaults.filledTonalButtonColors(
        containerColor = if (canPlay) {
            MaterialTheme.colorScheme.primaryContainer
        } else {
            MaterialTheme.colorScheme.surfaceVariant
        },
        contentColor = if (canPlay) {
            MaterialTheme.colorScheme.onPrimaryContainer
        } else {
            MaterialTheme.colorScheme.onSurfaceVariant
        }
    )
    
    // 脉冲动画（播放时）
    val scale by animateFloatAsState(
        targetValue = if (isPlaying) 1.1f else 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale"
    )
    
    FilledTonalButton(
        onClick = onClick,
        enabled = canPlay,
        modifier = modifier.then(
            if (isPlaying) Modifier.scale(scale) else Modifier
        ),
        shape = CircleShape,
        colors = buttonColors,
        contentPadding = PaddingValues(0.dp)
    ) {
        Icon(
            imageVector = Icons.Default.PlayArrow,
            contentDescription = "播放",
            modifier = Modifier.size(32.dp)
        )
    }
}

/**
 * 停止按钮组件
 */
@Composable
private fun StopButton(
    canStop: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        enabled = canStop,
        modifier = modifier,
        shape = CircleShape,
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = if (canStop) {
                MaterialTheme.colorScheme.error
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant
            }
        ),
        border = ButtonDefaults.outlinedButtonBorder.copy(
            brush = null,
            width = 2.dp
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Stop,
            contentDescription = "停止",
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * 播放提示文本
 */
@Composable
private fun PlaybackHint(
    canPlay: Boolean,
    isPlaying: Boolean,
    isInitialized: Boolean,
    modifier: Modifier = Modifier
) {
    val hintText = when {
        !isInitialized -> "正在初始化TTS引擎，请稍候..."
        isPlaying -> "点击停止按钮可以中断播放"
        canPlay -> "点击播放按钮开始朗读文本"
        else -> "请先输入要朗读的文本内容"
    }
    
    AnimatedVisibility(
        visible = true,
        enter = fadeIn() + slideInVertically(),
        exit = fadeOut() + slideOutVertically()
    ) {
        Text(
            text = hintText,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = modifier
        )
    }
}

/**
 * 紧凑版播放控制组件
 */
@Composable
fun CompactPlayControl(
    canPlay: Boolean,
    canStop: Boolean,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    onStopClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 播放/暂停按钮
        IconButton(
            onClick = onPlayClick,
            enabled = canPlay,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "播放",
                tint = if (canPlay) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
        }
        
        // 停止按钮
        IconButton(
            onClick = onStopClick,
            enabled = canStop,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Stop,
                contentDescription = "停止",
                tint = if (canStop) {
                    MaterialTheme.colorScheme.error
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
        }
        
        // 状态文本
        if (isPlaying) {
            Text(
                text = "播放中...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * 播放进度指示器（模拟）
 */
@Composable
fun PlaybackProgress(
    isPlaying: Boolean,
    modifier: Modifier = Modifier
) {
    var progress by remember { mutableFloatStateOf(0f) }
    
    LaunchedEffect(isPlaying) {
        if (isPlaying) {
            // 模拟播放进度
            while (progress < 1f && isPlaying) {
                kotlinx.coroutines.delay(100)
                progress += 0.01f
            }
        } else {
            progress = 0f
        }
    }
    
    AnimatedVisibility(
        visible = isPlaying,
        enter = fadeIn() + expandVertically(),
        exit = fadeOut() + shrinkVertically()
    ) {
        Column(
            modifier = modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            LinearProgressIndicator(
                progress = { progress },
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary,
                trackColor = MaterialTheme.colorScheme.surfaceVariant
            )
            
            Text(
                text = "${(progress * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PlayControlSectionPreview() {
    TTSAndroidDemoTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 准备就绪状态
            PlayControlSection(
                canPlay = true,
                canStop = false,
                isPlaying = false,
                isInitialized = true,
                onPlayClick = { },
                onStopClick = { }
            )
            
            // 播放中状态
            PlayControlSection(
                canPlay = false,
                canStop = true,
                isPlaying = true,
                isInitialized = true,
                onPlayClick = { },
                onStopClick = { }
            )
            
            // 紧凑版控制
            CompactPlayControl(
                canPlay = true,
                canStop = false,
                isPlaying = false,
                onPlayClick = { },
                onStopClick = { }
            )
        }
    }
}