package com.example.ttsandroiddemo.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.ttsandroiddemo.ui.theme.TTSAndroidDemoTheme

/**
 * 播放状态卡片组件
 */
@Composable
fun PlaybackStatusCard(
    isInitialized: <PERSON>olean,
    isPlaying: <PERSON><PERSON><PERSON>,
    canPlay: <PERSON><PERSON>an,
    errorMessage: String?,
    infoMessage: String?,
    modifier: Modifier = Modifier
) {
    val statusInfo = getStatusInfo(
        isInitialized = isInitialized,
        isPlaying = isPlaying,
        canPlay = canPlay,
        errorMessage = errorMessage,
        infoMessage = infoMessage
    )
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = statusInfo.backgroundColor
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 状态图标（带动画）
            StatusIcon(
                icon = statusInfo.icon,
                color = statusInfo.iconColor,
                isAnimated = statusInfo.isAnimated
            )
            
            // 状态文本
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = statusInfo.title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = statusInfo.textColor
                )
                
                if (statusInfo.description.isNotEmpty()) {
                    Text(
                        text = statusInfo.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = statusInfo.textColor.copy(alpha = 0.8f)
                    )
                }
            }
            
            // 操作按钮（如果需要）
            statusInfo.actionButton?.let { action ->
                TextButton(
                    onClick = action.onClick,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = statusInfo.textColor
                    )
                ) {
                    Text(action.text)
                }
            }
        }
    }
}

/**
 * 状态图标组件（带动画）
 */
@Composable
private fun StatusIcon(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    isAnimated: Boolean,
    modifier: Modifier = Modifier
) {
    // 旋转动画
    val rotation by animateFloatAsState(
        targetValue = if (isAnimated) 360f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    // 脉冲动画
    val scale by animateFloatAsState(
        targetValue = if (isAnimated) 1.2f else 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale"
    )
    
    Box(
        modifier = modifier
            .size(40.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(color.copy(alpha = 0.1f)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier
                .size(24.dp)
                .then(
                    if (isAnimated) {
                        Modifier
                            .rotate(rotation)
                            .scale(scale)
                    } else {
                        Modifier
                    }
                )
        )
    }
}

/**
 * 获取状态信息
 */
private fun getStatusInfo(
    isInitialized: Boolean,
    isPlaying: Boolean,
    canPlay: Boolean,
    errorMessage: String?,
    infoMessage: String?
): StatusInfo {
    return when {
        errorMessage != null -> StatusInfo(
            title = "错误",
            description = errorMessage,
            icon = Icons.Default.Error,
            iconColor = Color(0xFFD32F2F),
            textColor = Color(0xFFD32F2F),
            backgroundColor = Color(0xFFFFEBEE),
            isAnimated = false
        )
        
        !isInitialized -> StatusInfo(
            title = "初始化中",
            description = "正在启动TTS引擎，请稍候...",
            icon = Icons.Default.Sync,
            iconColor = Color(0xFF1976D2),
            textColor = Color(0xFF1976D2),
            backgroundColor = Color(0xFFE3F2FD),
            isAnimated = true
        )
        
        isPlaying -> StatusInfo(
            title = "播放中",
            description = infoMessage ?: "正在朗读文本内容",
            icon = Icons.Default.VolumeUp,
            iconColor = Color(0xFF388E3C),
            textColor = Color(0xFF388E3C),
            backgroundColor = Color(0xFFE8F5E8),
            isAnimated = true
        )
        
        canPlay -> StatusInfo(
            title = "准备就绪",
            description = infoMessage ?: "可以开始播放文本",
            icon = Icons.Default.CheckCircle,
            iconColor = Color(0xFF388E3C),
            textColor = Color(0xFF388E3C),
            backgroundColor = Color(0xFFE8F5E8),
            isAnimated = false
        )
        
        else -> StatusInfo(
            title = "等待输入",
            description = "请输入要朗读的文本内容",
            icon = Icons.Default.Edit,
            iconColor = Color(0xFF757575),
            textColor = Color(0xFF757575),
            backgroundColor = Color(0xFFF5F5F5),
            isAnimated = false
        )
    }
}

/**
 * 状态信息数据类
 */
private data class StatusInfo(
    val title: String,
    val description: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val iconColor: Color,
    val textColor: Color,
    val backgroundColor: Color,
    val isAnimated: Boolean,
    val actionButton: ActionButton? = null
)

/**
 * 操作按钮数据类
 */
private data class ActionButton(
    val text: String,
    val onClick: () -> Unit
)

/**
 * 播放控制快捷栏
 */
@Composable
fun PlaybackQuickBar(
    canPlay: Boolean,
    canStop: Boolean,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    onStopClick: () -> Unit,
    onSettingsClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surfaceVariant,
        tonalElevation = 3.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 播放状态文本
            Text(
                text = when {
                    isPlaying -> "播放中"
                    canPlay -> "就绪"
                    else -> "等待"
                },
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            // 控制按钮组
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 播放按钮
                IconButton(
                    onClick = onPlayClick,
                    enabled = canPlay
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "播放",
                        tint = if (canPlay) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                
                // 停止按钮
                IconButton(
                    onClick = onStopClick,
                    enabled = canStop
                ) {
                    Icon(
                        imageVector = Icons.Default.Stop,
                        contentDescription = "停止",
                        tint = if (canStop) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                
                // 设置按钮
                IconButton(onClick = onSettingsClick) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置"
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PlaybackStatusCardPreview() {
    TTSAndroidDemoTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 初始化状态
            PlaybackStatusCard(
                isInitialized = false,
                isPlaying = false,
                canPlay = false,
                errorMessage = null,
                infoMessage = null
            )
            
            // 播放状态
            PlaybackStatusCard(
                isInitialized = true,
                isPlaying = true,
                canPlay = false,
                errorMessage = null,
                infoMessage = "正在朗读示例文本"
            )
            
            // 错误状态
            PlaybackStatusCard(
                isInitialized = true,
                isPlaying = false,
                canPlay = false,
                errorMessage = "TTS引擎初始化失败",
                infoMessage = null
            )
            
            // 快捷栏
            PlaybackQuickBar(
                canPlay = true,
                canStop = false,
                isPlaying = false,
                onPlayClick = { },
                onStopClick = { },
                onSettingsClick = { }
            )
        }
    }
}