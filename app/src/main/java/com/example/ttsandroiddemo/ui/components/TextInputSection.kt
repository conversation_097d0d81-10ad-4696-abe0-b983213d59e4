package com.example.ttsandroiddemo.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.ttsandroiddemo.ui.theme.TTSAndroidDemoTheme

/**
 * 文本输入区域组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TextInputSection(
    inputText: String,
    isTextValid: Boolean,
    errorMessage: String?,
    onTextChange: (String) -> Unit,
    onClearText: () -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "请输入要朗读的文本...",
    maxLines: Int = 8,
    enabled: Boolean = true
) {
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 文本输入框
        OutlinedTextField(
            value = inputText,
            onValueChange = onTextChange,
            modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusRequester),
            placeholder = {
                Text(
                    text = placeholder,
                    style = MaterialTheme.typography.bodyLarge
                )
            },
            label = {
                Text("输入文本")
            },
            supportingText = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 错误消息或字符计数
                    if (errorMessage != null && !isTextValid) {
                        Text(
                            text = errorMessage,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    } else {
                        Text(
                            text = "${inputText.length} / 5000",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (inputText.length > 4500) {
                                MaterialTheme.colorScheme.error
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                }
            },
            trailingIcon = {
                if (inputText.isNotEmpty()) {
                    IconButton(
                        onClick = onClearText,
                        enabled = enabled
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "清除文本",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            },
            isError = !isTextValid,
            enabled = enabled,
            maxLines = maxLines,
            keyboardOptions = KeyboardOptions(
                capitalization = KeyboardCapitalization.Sentences,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    keyboardController?.hide()
                }
            ),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = if (isTextValid) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.error
                },
                unfocusedBorderColor = if (isTextValid) {
                    MaterialTheme.colorScheme.outline
                } else {
                    MaterialTheme.colorScheme.error
                }
            )
        )
        
        // 文本统计信息
        TextStatistics(
            text = inputText,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 文本统计信息组件
 */
@Composable
private fun TextStatistics(
    text: String,
    modifier: Modifier = Modifier
) {
    val wordCount = remember(text) {
        if (text.isBlank()) 0 else text.trim().split(Regex("\\s+")).size
    }
    
    val lineCount = remember(text) {
        if (text.isEmpty()) 0 else text.lines().size
    }
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        StatisticItem(
            label = "字符",
            value = text.length.toString(),
            modifier = Modifier.weight(1f)
        )
        
        StatisticItem(
            label = "词语",
            value = wordCount.toString(),
            modifier = Modifier.weight(1f)
        )
        
        StatisticItem(
            label = "行数",
            value = lineCount.toString(),
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 文本输入快捷操作组件
 */
@Composable
fun TextInputActions(
    onPasteFromClipboard: () -> Unit,
    onClearText: () -> Unit,
    onSelectSampleText: () -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        OutlinedButton(
            onClick = onPasteFromClipboard,
            enabled = enabled,
            modifier = Modifier.weight(1f)
        ) {
            Text("粘贴")
        }
        
        OutlinedButton(
            onClick = onSelectSampleText,
            enabled = enabled,
            modifier = Modifier.weight(1f)
        ) {
            Text("示例文本")
        }
        
        OutlinedButton(
            onClick = onClearText,
            enabled = enabled,
            modifier = Modifier.weight(1f)
        ) {
            Text("清除")
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun TextInputSectionPreview() {
    TTSAndroidDemoTheme {
        var text by remember { mutableStateOf("这是一个示例文本，用于测试TTS功能。") }
        
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            TextInputSection(
                inputText = text,
                isTextValid = true,
                errorMessage = null,
                onTextChange = { text = it },
                onClearText = { text = "" }
            )
            
            TextInputActions(
                onPasteFromClipboard = { },
                onClearText = { text = "" },
                onSelectSampleText = { text = "这是一个示例文本，用于测试TTS功能。" }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun TextInputSectionErrorPreview() {
    TTSAndroidDemoTheme {
        TextInputSection(
            inputText = "",
            isTextValid = false,
            errorMessage = "请输入要朗读的文本",
            onTextChange = { },
            onClearText = { },
            modifier = Modifier.padding(16.dp)
        )
    }
}