package com.example.ttsandroiddemo.tts

import android.speech.tts.Voice
import com.example.ttsandroiddemo.data.TTSParameterValidator
import com.example.ttsandroiddemo.data.TTSSettings
import java.util.Locale

/**
 * TTS参数管理器，用于管理和验证TTS参数
 */
class TTSParameterManager {
    
    private var currentSettings = TTSSettings.getDefault()
    
    /**
     * 更新设置
     */
    fun updateSettings(settings: TTSSettings) {
        currentSettings = TTSParameterValidator.validateSettings(settings)
    }
    
    /**
     * 获取当前设置
     */
    fun getCurrentSettings(): TTSSettings = currentSettings
    
    /**
     * 更新语言
     */
    fun updateLanguage(languageCode: String) {
        if (TTSParameterValidator.isValidLanguageCode(languageCode)) {
            currentSettings = currentSettings.copy(languageCode = languageCode)
        }
    }
    
    /**
     * 更新音色
     */
    fun updateVoice(voiceId: String?) {
        currentSettings = currentSettings.copy(voiceId = voiceId)
    }
    
    /**
     * 更新语速
     */
    fun updateSpeechRate(rate: Float) {
        val validatedRate = TTSParameterValidator.validateSpeechRate(rate)
        currentSettings = currentSettings.copy(speechRate = validatedRate)
    }
    
    /**
     * 更新音高
     */
    fun updatePitch(pitch: Float) {
        val validatedPitch = TTSParameterValidator.validatePitch(pitch)
        currentSettings = currentSettings.copy(pitch = validatedPitch)
    }
    
    /**
     * 更新音量
     */
    fun updateVolume(volume: Float) {
        val validatedVolume = TTSParameterValidator.validateVolume(volume)
        currentSettings = currentSettings.copy(volume = validatedVolume)
    }
    
    /**
     * 重置到默认设置
     */
    fun resetToDefaults() {
        currentSettings = TTSSettings.getDefault()
    }
    
    /**
     * 转换为TTS配置
     */
    fun toTTSConfiguration(availableVoices: Set<Voice> = emptySet()): TTSConfiguration {
        val locale = try {
            Locale.forLanguageTag(currentSettings.languageCode)
        } catch (e: Exception) {
            Locale.getDefault()
        }
        
        val voice = currentSettings.voiceId?.let { voiceId ->
            availableVoices.find { it.name == voiceId }
        }
        
        return TTSConfiguration(
            locale = locale,
            voice = voice,
            speechRate = currentSettings.speechRate,
            pitch = currentSettings.pitch
        )
    }
    
    /**
     * 从TTS配置更新设置
     */
    fun fromTTSConfiguration(config: TTSConfiguration) {
        currentSettings = currentSettings.copy(
            languageCode = config.locale.toLanguageTag(),
            voiceId = config.voice?.name,
            speechRate = config.speechRate,
            pitch = config.pitch
        )
    }
    
    /**
     * 验证当前设置是否有效
     */
    fun isCurrentSettingsValid(): Boolean {
        return TTSParameterValidator.isValidLanguageCode(currentSettings.languageCode) &&
               currentSettings.speechRate in TTSParameterValidator.MIN_SPEECH_RATE..TTSParameterValidator.MAX_SPEECH_RATE &&
               currentSettings.pitch in TTSParameterValidator.MIN_PITCH..TTSParameterValidator.MAX_PITCH &&
               currentSettings.volume in TTSParameterValidator.MIN_VOLUME..TTSParameterValidator.MAX_VOLUME
    }
    
    /**
     * 获取参数范围信息
     */
    fun getParameterRanges(): ParameterRanges {
        return ParameterRanges(
            speechRateRange = TTSParameterValidator.MIN_SPEECH_RATE to TTSParameterValidator.MAX_SPEECH_RATE,
            pitchRange = TTSParameterValidator.MIN_PITCH to TTSParameterValidator.MAX_PITCH,
            volumeRange = TTSParameterValidator.MIN_VOLUME to TTSParameterValidator.MAX_VOLUME
        )
    }
}

/**
 * 参数范围数据类
 */
data class ParameterRanges(
    val speechRateRange: Pair<Float, Float>,
    val pitchRange: Pair<Float, Float>,
    val volumeRange: Pair<Float, Float>
)