package com.example.ttsandroiddemo.tts

import android.speech.tts.Voice
import java.util.Locale

/**
 * TTS事件监听器接口
 */
interface TTSEventListener {
    
    /**
     * TTS初始化完成
     */
    fun onInitialized(success: Boolean) {}
    
    /**
     * 开始播放
     */
    fun onSpeakingStarted(text: String) {}
    
    /**
     * 播放完成
     */
    fun onSpeakingCompleted() {}
    
    /**
     * 播放错误
     */
    fun onSpeakingError(error: String) {}
    
    /**
     * 语言变更
     */
    fun onLanguageChanged(locale: Locale, success: Boolean) {}
    
    /**
     * 音色变更
     */
    fun onVoiceChanged(voice: Voice, success: Boolean) {}
    
    /**
     * 参数变更
     */
    fun onParametersChanged(speechRate: Float, pitch: Float) {}
    
    /**
     * 音量变更
     */
    fun onVolumeChanged(volume: Float) {}
}

/**
 * 简单的TTS事件监听器实现
 */
open class SimpleTTSEventListener : TTSEventListener {
    // 提供默认的空实现，子类可以选择性重写需要的方法
}