package com.example.ttsandroiddemo.tts

import android.speech.tts.Voice
import java.util.Locale

/**
 * TTS配置数据类
 */
data class TTSConfiguration(
    val locale: Locale = Locale.getDefault(),
    val voice: Voice? = null,
    val speechRate: Float = 1.0f,
    val pitch: Float = 1.0f
) {
    companion object {
        fun default() = TTSConfiguration()
        
        fun chinese() = TTSConfiguration(
            locale = Locale.forLanguageTag("zh-CN")
        )
        
        fun english() = TTSConfiguration(
            locale = Locale.forLanguageTag("en-US")
        )
    }
    
    /**
     * 验证配置参数是否有效
     */
    fun isValid(): Boolean {
        return speechRate in 0.1f..3.0f && pitch in 0.1f..2.0f
    }
    
    /**
     * 获取修正后的配置
     */
    fun corrected(): TTSConfiguration {
        return copy(
            speechRate = speechRate.coerceIn(0.1f, 3.0f),
            pitch = pitch.coerceIn(0.1f, 2.0f)
        )
    }
}