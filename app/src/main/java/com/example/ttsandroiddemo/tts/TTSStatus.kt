package com.example.ttsandroiddemo.tts

/**
 * TTS状态枚举类
 */
enum class TTSStatus {
    UNINITIALIZED,
    INITIALIZING,
    READY,
    PLAYING,
    ERROR
}

/**
 * TTS错误类型枚举
 */
enum class TTSError {
    INITIALIZATION_FAILED,
    LANGUAGE_NOT_SUPPORTED,
    VOICE_NOT_SUPPORTED,
    PLAYBACK_FAILED,
    EMPTY_TEXT,
    UNKNOWN_ERROR
}

/**
 * TTS状态数据类
 */
data class TTSState(
    val status: TTSStatus = TTSStatus.UNINITIALIZED,
    val error: TTSError? = null,
    val errorMessage: String? = null,
    val isInitialized: Boolean = false,
    val isPlaying: Boolean = false
) {
    companion object {
        fun initial() = TTSState()
        
        fun ready() = TTSState(
            status = TTSStatus.READY,
            isInitialized = true
        )
        
        fun playing() = TTSState(
            status = TTSStatus.PLAYING,
            isInitialized = true,
            isPlaying = true
        )
        
        fun error(error: TTSError, message: String) = TTSState(
            status = TTSStatus.ERROR,
            error = error,
            errorMessage = message
        )
    }
}