package com.example.ttsandroiddemo.tts

import android.content.Context
import android.media.AudioManager
import android.speech.tts.TextToSpeech
import android.speech.tts.Voice
import android.util.Log
import com.example.ttsandroiddemo.data.LanguageItem
import com.example.ttsandroiddemo.data.VoiceItem
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Locale

/**
 * TTS引擎管理器，封装Android TextToSpeech API
 */
class TTSManager : TextToSpeech.OnInitListener {
    
    companion object {
        private const val TAG = "TTSManager"
    }
    
    private var textToSpeech: TextToSpeech? = null
    private var audioManager: AudioManager? = null
    private var initCallback: ((Boolean) -> Unit)? = null
    private var context: Context? = null
    
    // 状态管理
    private val _isInitialized = MutableStateFlow(false)
    val isInitialized: StateFlow<Boolean> = _isInitialized.asStateFlow()
    
    private val _isPlaying = MutableStateFlow(false)
    val isPlaying: StateFlow<Boolean> = _isPlaying.asStateFlow()
    
    private val _currentError = MutableStateFlow<String?>(null)
    val currentError: StateFlow<String?> = _currentError.asStateFlow()
    
    /**
     * 初始化TTS引擎
     */
    fun initialize(context: Context, onInitComplete: (Boolean) -> Unit) {
        try {
            this.context = context.applicationContext
            initCallback = onInitComplete
            audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            textToSpeech = TextToSpeech(context.applicationContext, this)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize TTS", e)
            _currentError.value = "TTS初始化失败: ${e.message}"
            onInitComplete(false)
        }
    }
    
    /**
     * TTS初始化回调
     */
    override fun onInit(status: Int) {
        val success = status == TextToSpeech.SUCCESS
        _isInitialized.value = success
        
        if (success) {
            Log.d(TAG, "TTS initialized successfully")
            _currentError.value = null
            setupTTSListener()
        } else {
            Log.e(TAG, "TTS initialization failed with status: $status")
            _currentError.value = "TTS引擎初始化失败"
        }
        
        initCallback?.invoke(success)
        initCallback = null
    }
    
    /**
     * 设置TTS监听器
     */
    private fun setupTTSListener() {
        textToSpeech?.setOnUtteranceProgressListener(object : android.speech.tts.UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                _isPlaying.value = true
                Log.d(TAG, "TTS started speaking")
            }
            
            override fun onDone(utteranceId: String?) {
                _isPlaying.value = false
                Log.d(TAG, "TTS finished speaking")
            }
            
            override fun onError(utteranceId: String?) {
                _isPlaying.value = false
                _currentError.value = "播放过程中发生错误"
                Log.e(TAG, "TTS error during playback")
            }
        })
    }
    
    /**
     * 朗读文本
     */
    fun speak(text: String) {
        if (!_isInitialized.value) {
            _currentError.value = "TTS引擎未初始化"
            return
        }
        
        if (text.isBlank()) {
            _currentError.value = "文本内容为空"
            return
        }
        
        try {
            val result = textToSpeech?.speak(
                text,
                TextToSpeech.QUEUE_FLUSH,
                null,
                "TTS_UTTERANCE_ID"
            )
            
            if (result == TextToSpeech.ERROR) {
                _currentError.value = "播放失败"
                Log.e(TAG, "TTS speak failed")
            } else {
                _currentError.value = null
                Log.d(TAG, "TTS speak started")
            }
        } catch (e: Exception) {
            _currentError.value = "播放异常: ${e.message}"
            Log.e(TAG, "Exception during TTS speak", e)
        }
    }
    
    /**
     * 停止朗读
     */
    fun stop() {
        try {
            textToSpeech?.stop()
            _isPlaying.value = false
            Log.d(TAG, "TTS stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping TTS", e)
        }
    }
    
    /**
     * 设置语言
     */
    fun setLanguage(locale: Locale): Boolean {
        return try {
            val result = textToSpeech?.setLanguage(locale)
            when (result) {
                TextToSpeech.LANG_MISSING_DATA,
                TextToSpeech.LANG_NOT_SUPPORTED -> {
                    _currentError.value = "不支持的语言: ${locale.displayLanguage}"
                    Log.w(TAG, "Language not supported: $locale")
                    false
                }
                TextToSpeech.LANG_AVAILABLE,
                TextToSpeech.LANG_COUNTRY_AVAILABLE,
                TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE -> {
                    _currentError.value = null
                    Log.d(TAG, "Language set to: $locale")
                    true
                }
                else -> {
                    _currentError.value = "设置语言失败"
                    Log.e(TAG, "Failed to set language: $locale")
                    false
                }
            }
        } catch (e: Exception) {
            _currentError.value = "设置语言异常: ${e.message}"
            Log.e(TAG, "Exception setting language", e)
            false
        }
    }
    
    /**
     * 设置音色
     */
    fun setVoice(voice: Voice): Boolean {
        return try {
            val result = textToSpeech?.setVoice(voice)
            if (result == TextToSpeech.SUCCESS) {
                _currentError.value = null
                Log.d(TAG, "Voice set to: ${voice.name}")
                true
            } else {
                _currentError.value = "设置音色失败"
                Log.e(TAG, "Failed to set voice: ${voice.name}")
                false
            }
        } catch (e: Exception) {
            _currentError.value = "设置音色异常: ${e.message}"
            Log.e(TAG, "Exception setting voice", e)
            false
        }
    }
    
    /**
     * 设置语速
     */
    fun setSpeechRate(rate: Float) {
        try {
            val result = textToSpeech?.setSpeechRate(rate)
            if (result == TextToSpeech.SUCCESS) {
                Log.d(TAG, "Speech rate set to: $rate")
            } else {
                Log.w(TAG, "Failed to set speech rate: $rate")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception setting speech rate", e)
        }
    }
    
    /**
     * 设置音高
     */
    fun setPitch(pitch: Float) {
        try {
            val result = textToSpeech?.setPitch(pitch)
            if (result == TextToSpeech.SUCCESS) {
                Log.d(TAG, "Pitch set to: $pitch")
            } else {
                Log.w(TAG, "Failed to set pitch: $pitch")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception setting pitch", e)
        }
    }
    
    /**
     * 获取可用语言
     */
    fun getAvailableLanguages(): Set<Locale> {
        return try {
            textToSpeech?.availableLanguages ?: emptySet()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available languages", e)
            emptySet()
        }
    }
    
    /**
     * 获取可用音色
     */
    fun getAvailableVoices(): Set<Voice> {
        return try {
            textToSpeech?.voices ?: emptySet()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available voices", e)
            emptySet()
        }
    }
    
    /**
     * 获取指定语言的可用音色
     */
    fun getVoicesForLanguage(locale: Locale): Set<Voice> {
        return try {
            getAvailableVoices().filter { voice ->
                voice.locale.language == locale.language &&
                (voice.locale.country.isEmpty() || voice.locale.country == locale.country)
            }.toSet()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting voices for language: $locale", e)
            emptySet()
        }
    }
    
    /**
     * 获取可用语言列表（作为LanguageItem）
     */
    fun getAvailableLanguageItems(): List<LanguageItem> {
        return try {
            getAvailableLanguages().map { locale ->
                LanguageItem.fromLocale(locale, true)
            }.sortedBy { it.displayName }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting language items", e)
            LanguageItem.getCommonLanguages()
        }
    }
    
    /**
     * 获取可用音色列表（作为VoiceItem）
     */
    fun getAvailableVoiceItems(): List<VoiceItem> {
        return try {
            VoiceItem.fromVoiceSet(getAvailableVoices())
        } catch (e: Exception) {
            Log.e(TAG, "Error getting voice items", e)
            emptyList()
        }
    }
    
    /**
     * 获取指定语言的音色列表（作为VoiceItem）
     */
    fun getVoiceItemsForLanguage(locale: Locale): List<VoiceItem> {
        return try {
            VoiceItem.fromVoiceSet(getVoicesForLanguage(locale))
        } catch (e: Exception) {
            Log.e(TAG, "Error getting voice items for language: $locale", e)
            emptyList()
        }
    }
    
    /**
     * 设置音量（通过AudioManager）
     */
    fun setVolume(volume: Float) {
        try {
            audioManager?.let { am ->
                val maxVolume = am.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                val targetVolume = (volume * maxVolume).toInt().coerceIn(0, maxVolume)
                am.setStreamVolume(AudioManager.STREAM_MUSIC, targetVolume, 0)
                Log.d(TAG, "Volume set to: $volume (system: $targetVolume/$maxVolume)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception setting volume", e)
        }
    }
    
    /**
     * 获取当前音量
     */
    fun getCurrentVolume(): Float {
        return try {
            audioManager?.let { am ->
                val currentVolume = am.getStreamVolume(AudioManager.STREAM_MUSIC)
                val maxVolume = am.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
                if (maxVolume > 0) currentVolume.toFloat() / maxVolume else 0f
            } ?: 0f
        } catch (e: Exception) {
            Log.e(TAG, "Exception getting volume", e)
            0f
        }
    }
    
    /**
     * 检查语言是否可用
     */
    fun isLanguageAvailable(locale: Locale): Boolean {
        return try {
            val result = textToSpeech?.isLanguageAvailable(locale)
            result == TextToSpeech.LANG_AVAILABLE ||
            result == TextToSpeech.LANG_COUNTRY_AVAILABLE ||
            result == TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE
        } catch (e: Exception) {
            Log.e(TAG, "Error checking language availability", e)
            false
        }
    }
    
    /**
     * 获取当前设置的语言
     */
    fun getCurrentLanguage(): Locale? {
        return try {
            textToSpeech?.language
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current language", e)
            null
        }
    }
    
    /**
     * 获取当前设置的音色
     */
    fun getCurrentVoice(): Voice? {
        return try {
            textToSpeech?.voice
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current voice", e)
            null
        }
    }
    
    /**
     * 应用TTS配置
     */
    fun applyConfiguration(config: TTSConfiguration): Boolean {
        if (!_isInitialized.value) {
            _currentError.value = "TTS引擎未初始化"
            return false
        }
        
        return try {
            val correctedConfig = config.corrected()
            var success = true
            
            // 设置语言
            if (!setLanguage(correctedConfig.locale)) {
                success = false
            }
            
            // 设置音色（如果指定）
            correctedConfig.voice?.let { voice ->
                if (!setVoice(voice)) {
                    success = false
                }
            }
            
            // 设置语速和音高
            setSpeechRate(correctedConfig.speechRate)
            setPitch(correctedConfig.pitch)
            
            if (success) {
                Log.d(TAG, "Configuration applied successfully")
            } else {
                Log.w(TAG, "Configuration applied with some failures")
            }
            
            success
        } catch (e: Exception) {
            _currentError.value = "应用配置异常: ${e.message}"
            Log.e(TAG, "Exception applying configuration", e)
            false
        }
    }
    
    /**
     * 获取当前TTS配置
     */
    fun getCurrentConfiguration(): TTSConfiguration? {
        return try {
            val currentLanguage = getCurrentLanguage()
            val currentVoice = getCurrentVoice()
            
            if (currentLanguage != null) {
                TTSConfiguration(
                    locale = currentLanguage,
                    voice = currentVoice,
                    speechRate = 1.0f, // TTS API不提供获取当前值的方法
                    pitch = 1.0f
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current configuration", e)
            null
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            textToSpeech?.stop()
            textToSpeech?.shutdown()
            textToSpeech = null
            audioManager = null
            context = null
            _isInitialized.value = false
            _isPlaying.value = false
            _currentError.value = null
            Log.d(TAG, "TTS resources released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing TTS resources", e)
        }
    }
}