package com.example.ttsandroiddemo.storage

import android.content.Context
import android.util.Log
import com.example.ttsandroiddemo.data.TTSSettings
import com.example.ttsandroiddemo.data.validated
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map

/**
 * TTS设置仓库，提供高级设置管理接口
 */
class TTSSettingsRepository(context: Context) {
    
    companion object {
        private const val TAG = "TTSSettingsRepository"
        
        @Volatile
        private var INSTANCE: TTSSettingsRepository? = null
        
        fun getInstance(context: Context): TTSSettingsRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TTSSettingsRepository(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val preferencesManager = TTSPreferencesManager(context)
    
    /**
     * 设置流，自动验证设置
     */
    val settingsFlow: StateFlow<TTSSettings> = preferencesManager.settingsFlow
    
    /**
     * 验证后的设置流
     */
    val validatedSettingsFlow = settingsFlow.map { it.validated() }
    
    /**
     * 保存设置
     */
    suspend fun saveSettings(settings: TTSSettings) {
        try {
            val validatedSettings = settings.validated()
            preferencesManager.saveSettings(validatedSettings)
            Log.d(TAG, "Settings saved and validated")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save settings", e)
            throw e
        }
    }
    
    /**
     * 获取当前设置
     */
    fun getCurrentSettings(): TTSSettings {
        return preferencesManager.getCurrentSettings().validated()
    }
    
    /**
     * 更新语言代码
     */
    suspend fun updateLanguageCode(languageCode: String) {
        try {
            preferencesManager.saveLanguageCode(languageCode)
            Log.d(TAG, "Language code updated: $languageCode")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update language code", e)
            throw e
        }
    }
    
    /**
     * 更新音色ID
     */
    suspend fun updateVoiceId(voiceId: String?) {
        try {
            preferencesManager.saveVoiceId(voiceId)
            Log.d(TAG, "Voice ID updated: $voiceId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update voice ID", e)
            throw e
        }
    }
    
    /**
     * 更新语速
     */
    suspend fun updateSpeechRate(speechRate: Float) {
        try {
            preferencesManager.saveSpeechRate(speechRate)
            Log.d(TAG, "Speech rate updated: $speechRate")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update speech rate", e)
            throw e
        }
    }
    
    /**
     * 更新音高
     */
    suspend fun updatePitch(pitch: Float) {
        try {
            preferencesManager.savePitch(pitch)
            Log.d(TAG, "Pitch updated: $pitch")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update pitch", e)
            throw e
        }
    }
    
    /**
     * 更新音量
     */
    suspend fun updateVolume(volume: Float) {
        try {
            preferencesManager.saveVolume(volume)
            Log.d(TAG, "Volume updated: $volume")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update volume", e)
            throw e
        }
    }
    
    /**
     * 批量更新设置
     */
    suspend fun updateSettings(
        languageCode: String? = null,
        voiceId: String? = null,
        speechRate: Float? = null,
        pitch: Float? = null,
        volume: Float? = null
    ) {
        try {
            val currentSettings = getCurrentSettings()
            val updatedSettings = currentSettings.copy(
                languageCode = languageCode ?: currentSettings.languageCode,
                voiceId = voiceId ?: currentSettings.voiceId,
                speechRate = speechRate ?: currentSettings.speechRate,
                pitch = pitch ?: currentSettings.pitch,
                volume = volume ?: currentSettings.volume
            )
            saveSettings(updatedSettings)
            Log.d(TAG, "Settings updated in batch")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update settings in batch", e)
            throw e
        }
    }
    
    /**
     * 重置到默认设置
     */
    suspend fun resetToDefaults() {
        try {
            preferencesManager.resetToDefaults()
            Log.d(TAG, "Settings reset to defaults")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to reset settings", e)
            throw e
        }
    }
    
    /**
     * 导出设置
     */
    fun exportSettings(): String {
        return preferencesManager.exportSettings()
    }
    
    /**
     * 导入设置
     */
    suspend fun importSettings(jsonString: String): Boolean {
        return try {
            val success = preferencesManager.importSettings(jsonString)
            if (success) {
                Log.d(TAG, "Settings imported successfully")
            } else {
                Log.w(TAG, "Settings import failed")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Exception during settings import", e)
            false
        }
    }
    
    /**
     * 获取设置信息
     */
    fun getSettingsInfo(): SettingsInfo {
        return preferencesManager.getSettingsInfo()
    }
    
    /**
     * 检查设置是否为默认值
     */
    fun isDefaultSettings(): Boolean {
        val current = getCurrentSettings()
        val default = TTSSettings.getDefault()
        return current == default
    }
    
    /**
     * 获取设置变更历史（简单实现）
     */
    fun hasSettingsChanged(): Boolean {
        return !isDefaultSettings()
    }
    
    /**
     * 清除所有设置
     */
    suspend fun clearAllSettings() {
        try {
            preferencesManager.clearAllSettings()
            Log.d(TAG, "All settings cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear all settings", e)
            throw e
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        preferencesManager.release()
    }
}