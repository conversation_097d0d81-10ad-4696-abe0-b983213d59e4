package com.example.ttsandroiddemo.storage

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.ttsandroiddemo.data.TTSSettings
import com.example.ttsandroiddemo.data.TTSSettingsSerializer
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * TTS设置持久化管理器
 */
class TTSPreferencesManager(context: Context) {
    
    companion object {
        private const val TAG = "TTSPreferencesManager"
        private const val PREFS_NAME = "tts_settings"
        private const val KEY_SETTINGS = "tts_settings_json"
        private const val KEY_LANGUAGE_CODE = "language_code"
        private const val KEY_VOICE_ID = "voice_id"
        private const val KEY_SPEECH_RATE = "speech_rate"
        private const val KEY_PITCH = "pitch"
        private const val KEY_VOLUME = "volume"
        private const val KEY_FIRST_LAUNCH = "first_launch"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // 设置变更监听
    private val _settingsFlow = MutableStateFlow(loadSettings())
    val settingsFlow: StateFlow<TTSSettings> = _settingsFlow.asStateFlow()
    
    private val settingsChangeListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
        if (key == KEY_SETTINGS || key in listOf(KEY_LANGUAGE_CODE, KEY_VOICE_ID, KEY_SPEECH_RATE, KEY_PITCH, KEY_VOLUME)) {
            _settingsFlow.value = loadSettings()
            Log.d(TAG, "Settings updated due to preference change: $key")
        }
    }
    
    init {
        sharedPreferences.registerOnSharedPreferenceChangeListener(settingsChangeListener)
        
        // 如果是首次启动，保存默认设置
        if (isFirstLaunch()) {
            saveSettings(TTSSettings.getDefault())
            setFirstLaunchCompleted()
        }
    }
    
    /**
     * 保存设置（使用JSON序列化）
     */
    fun saveSettings(settings: TTSSettings) {
        try {
            val jsonString = TTSSettingsSerializer.serialize(settings)
            sharedPreferences.edit()
                .putString(KEY_SETTINGS, jsonString)
                .apply()
            
            _settingsFlow.value = settings
            Log.d(TAG, "Settings saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save settings", e)
        }
    }
    
    /**
     * 加载设置（从JSON反序列化）
     */
    fun loadSettings(): TTSSettings {
        return try {
            val jsonString = sharedPreferences.getString(KEY_SETTINGS, null)
            if (jsonString != null) {
                TTSSettingsSerializer.deserialize(jsonString)
            } else {
                // 尝试从旧的单独键值加载
                loadLegacySettings() ?: TTSSettings.getDefault()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load settings, using defaults", e)
            TTSSettings.getDefault()
        }
    }
    
    /**
     * 从旧版本的单独键值加载设置（向后兼容）
     */
    private fun loadLegacySettings(): TTSSettings? {
        return try {
            if (sharedPreferences.contains(KEY_LANGUAGE_CODE)) {
                TTSSettings(
                    languageCode = sharedPreferences.getString(KEY_LANGUAGE_CODE, "zh-CN") ?: "zh-CN",
                    voiceId = sharedPreferences.getString(KEY_VOICE_ID, null),
                    speechRate = sharedPreferences.getFloat(KEY_SPEECH_RATE, 1.0f),
                    pitch = sharedPreferences.getFloat(KEY_PITCH, 1.0f),
                    volume = sharedPreferences.getFloat(KEY_VOLUME, 1.0f)
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load legacy settings", e)
            null
        }
    }
    
    /**
     * 保存单个设置项
     */
    fun saveLanguageCode(languageCode: String) {
        val currentSettings = _settingsFlow.value
        saveSettings(currentSettings.copy(languageCode = languageCode))
    }
    
    fun saveVoiceId(voiceId: String?) {
        val currentSettings = _settingsFlow.value
        saveSettings(currentSettings.copy(voiceId = voiceId))
    }
    
    fun saveSpeechRate(speechRate: Float) {
        val currentSettings = _settingsFlow.value
        saveSettings(currentSettings.copy(speechRate = speechRate))
    }
    
    fun savePitch(pitch: Float) {
        val currentSettings = _settingsFlow.value
        saveSettings(currentSettings.copy(pitch = pitch))
    }
    
    fun saveVolume(volume: Float) {
        val currentSettings = _settingsFlow.value
        saveSettings(currentSettings.copy(volume = volume))
    }
    
    /**
     * 获取当前设置
     */
    fun getCurrentSettings(): TTSSettings {
        return _settingsFlow.value
    }
    
    /**
     * 重置到默认设置
     */
    fun resetToDefaults() {
        saveSettings(TTSSettings.getDefault())
        Log.d(TAG, "Settings reset to defaults")
    }
    
    /**
     * 清除所有设置
     */
    fun clearAllSettings() {
        try {
            sharedPreferences.edit().clear().apply()
            _settingsFlow.value = TTSSettings.getDefault()
            Log.d(TAG, "All settings cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear settings", e)
        }
    }
    
    /**
     * 检查是否为首次启动
     */
    private fun isFirstLaunch(): Boolean {
        return !sharedPreferences.getBoolean(KEY_FIRST_LAUNCH, false)
    }
    
    /**
     * 标记首次启动已完成
     */
    private fun setFirstLaunchCompleted() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_LAUNCH, true)
            .apply()
    }
    
    /**
     * 导出设置为JSON字符串
     */
    fun exportSettings(): String {
        return TTSSettingsSerializer.serialize(_settingsFlow.value)
    }
    
    /**
     * 从JSON字符串导入设置
     */
    fun importSettings(jsonString: String): Boolean {
        return try {
            val settings = TTSSettingsSerializer.deserialize(jsonString)
            saveSettings(settings)
            Log.d(TAG, "Settings imported successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to import settings", e)
            false
        }
    }
    
    /**
     * 获取设置统计信息
     */
    fun getSettingsInfo(): SettingsInfo {
        val settings = _settingsFlow.value
        return SettingsInfo(
            languageCode = settings.languageCode,
            hasCustomVoice = settings.voiceId != null,
            isDefaultSpeechRate = settings.speechRate == 1.0f,
            isDefaultPitch = settings.pitch == 1.0f,
            isDefaultVolume = settings.volume == 1.0f,
            isFirstLaunch = isFirstLaunch()
        )
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            sharedPreferences.unregisterOnSharedPreferenceChangeListener(settingsChangeListener)
            Log.d(TAG, "Preferences manager released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing preferences manager", e)
        }
    }
}

/**
 * 设置信息数据类
 */
data class SettingsInfo(
    val languageCode: String,
    val hasCustomVoice: Boolean,
    val isDefaultSpeechRate: Boolean,
    val isDefaultPitch: Boolean,
    val isDefaultVolume: Boolean,
    val isFirstLaunch: Boolean
)