package com.example.ttsandroiddemo.storage

import android.content.Context
import android.util.Log
import com.example.ttsandroiddemo.data.TTSSettings
import com.example.ttsandroiddemo.data.TTSSettingsSerializer
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * TTS设置备份和恢复工具
 */
class TTSSettingsBackup(private val context: Context) {
    
    companion object {
        private const val TAG = "TTSSettingsBackup"
        private const val BACKUP_DIR = "tts_backups"
        private const val BACKUP_FILE_PREFIX = "tts_settings_backup_"
        private const val BACKUP_FILE_EXTENSION = ".json"
        private const val MAX_BACKUP_FILES = 10
    }
    
    private val backupDir: File by lazy {
        File(context.filesDir, BACKUP_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    /**
     * 创建设置备份
     */
    fun createBackup(settings: TTSSettings, customName: String? = null): BackupResult {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = if (customName != null) {
                "${BACKUP_FILE_PREFIX}${customName}_$timestamp$BACKUP_FILE_EXTENSION"
            } else {
                "$BACKUP_FILE_PREFIX$timestamp$BACKUP_FILE_EXTENSION"
            }
            
            val backupFile = File(backupDir, fileName)
            val jsonString = TTSSettingsSerializer.serialize(settings)
            
            FileOutputStream(backupFile).use { fos ->
                fos.write(jsonString.toByteArray())
            }
            
            // 清理旧备份文件
            cleanupOldBackups()
            
            Log.d(TAG, "Backup created: ${backupFile.name}")
            BackupResult.Success(backupFile.name, backupFile.absolutePath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create backup", e)
            BackupResult.Error("创建备份失败: ${e.message}")
        }
    }
    
    /**
     * 恢复设置备份
     */
    fun restoreBackup(fileName: String): RestoreResult {
        return try {
            val backupFile = File(backupDir, fileName)
            if (!backupFile.exists()) {
                return RestoreResult.Error("备份文件不存在: $fileName")
            }
            
            val jsonString = FileInputStream(backupFile).use { fis ->
                fis.readBytes().toString(Charsets.UTF_8)
            }
            
            val settings = TTSSettingsSerializer.deserialize(jsonString)
            Log.d(TAG, "Backup restored: $fileName")
            RestoreResult.Success(settings)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore backup: $fileName", e)
            RestoreResult.Error("恢复备份失败: ${e.message}")
        }
    }
    
    /**
     * 获取所有备份文件
     */
    fun getBackupFiles(): List<BackupFileInfo> {
        return try {
            backupDir.listFiles { file ->
                file.isFile && file.name.startsWith(BACKUP_FILE_PREFIX) && file.name.endsWith(BACKUP_FILE_EXTENSION)
            }?.map { file ->
                BackupFileInfo(
                    fileName = file.name,
                    filePath = file.absolutePath,
                    fileSize = file.length(),
                    lastModified = file.lastModified(),
                    displayName = extractDisplayName(file.name)
                )
            }?.sortedByDescending { it.lastModified } ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get backup files", e)
            emptyList()
        }
    }
    
    /**
     * 删除备份文件
     */
    fun deleteBackup(fileName: String): Boolean {
        return try {
            val backupFile = File(backupDir, fileName)
            val deleted = backupFile.delete()
            if (deleted) {
                Log.d(TAG, "Backup deleted: $fileName")
            } else {
                Log.w(TAG, "Failed to delete backup: $fileName")
            }
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "Exception deleting backup: $fileName", e)
            false
        }
    }
    
    /**
     * 清理所有备份文件
     */
    fun clearAllBackups(): Boolean {
        return try {
            val files = backupDir.listFiles()
            var allDeleted = true
            files?.forEach { file ->
                if (!file.delete()) {
                    allDeleted = false
                }
            }
            if (allDeleted) {
                Log.d(TAG, "All backups cleared")
            } else {
                Log.w(TAG, "Some backups could not be deleted")
            }
            allDeleted
        } catch (e: Exception) {
            Log.e(TAG, "Exception clearing all backups", e)
            false
        }
    }
    
    /**
     * 清理旧的备份文件，保留最新的几个
     */
    private fun cleanupOldBackups() {
        try {
            val backupFiles = getBackupFiles()
            if (backupFiles.size > MAX_BACKUP_FILES) {
                val filesToDelete = backupFiles.drop(MAX_BACKUP_FILES)
                filesToDelete.forEach { backupInfo ->
                    deleteBackup(backupInfo.fileName)
                }
                Log.d(TAG, "Cleaned up ${filesToDelete.size} old backup files")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception during backup cleanup", e)
        }
    }
    
    /**
     * 从文件名提取显示名称
     */
    private fun extractDisplayName(fileName: String): String {
        return try {
            val nameWithoutExtension = fileName.removeSuffix(BACKUP_FILE_EXTENSION)
            val parts = nameWithoutExtension.removePrefix(BACKUP_FILE_PREFIX).split("_")
            
            if (parts.size >= 2) {
                val customName = parts.dropLast(2).joinToString("_")
                val date = parts[parts.size - 2]
                val time = parts[parts.size - 1]
                
                val formattedDate = "${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}"
                val formattedTime = "${time.substring(0, 2)}:${time.substring(2, 4)}:${time.substring(4, 6)}"
                
                if (customName.isNotEmpty()) {
                    "$customName ($formattedDate $formattedTime)"
                } else {
                    "$formattedDate $formattedTime"
                }
            } else {
                fileName
            }
        } catch (e: Exception) {
            fileName
        }
    }
    
    /**
     * 获取备份目录大小
     */
    fun getBackupDirectorySize(): Long {
        return try {
            backupDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
        } catch (e: Exception) {
            Log.e(TAG, "Exception calculating backup directory size", e)
            0L
        }
    }
}

/**
 * 备份结果密封类
 */
sealed class BackupResult {
    data class Success(val fileName: String, val filePath: String) : BackupResult()
    data class Error(val message: String) : BackupResult()
}

/**
 * 恢复结果密封类
 */
sealed class RestoreResult {
    data class Success(val settings: TTSSettings) : RestoreResult()
    data class Error(val message: String) : RestoreResult()
}

/**
 * 备份文件信息数据类
 */
data class BackupFileInfo(
    val fileName: String,
    val filePath: String,
    val fileSize: Long,
    val lastModified: Long,
    val displayName: String
)