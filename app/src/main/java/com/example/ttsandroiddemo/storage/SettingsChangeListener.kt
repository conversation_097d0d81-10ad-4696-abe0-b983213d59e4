package com.example.ttsandroiddemo.storage

import com.example.ttsandroiddemo.data.TTSSettings

/**
 * 设置变更监听器接口
 */
interface SettingsChangeListener {
    
    /**
     * 设置发生变更时调用
     */
    fun onSettingsChanged(oldSettings: TTSSettings, newSettings: TTSSettings)
    
    /**
     * 语言代码变更时调用
     */
    fun onLanguageCodeChanged(oldLanguageCode: String, newLanguageCode: String) {}
    
    /**
     * 音色ID变更时调用
     */
    fun onVoiceIdChanged(oldVoiceId: String?, newVoiceId: String?) {}
    
    /**
     * 语速变更时调用
     */
    fun onSpeechRateChanged(oldRate: Float, newRate: Float) {}
    
    /**
     * 音高变更时调用
     */
    fun onPitchChanged(oldPitch: Float, newPitch: Float) {}
    
    /**
     * 音量变更时调用
     */
    fun onVolumeChanged(oldVolume: Float, newVolume: Float) {}
    
    /**
     * 设置重置时调用
     */
    fun onSettingsReset() {}
}

/**
 * 简单的设置变更监听器实现
 */
open class SimpleSettingsChangeListener : SettingsChangeListener {
    override fun onSettingsChanged(oldSettings: TTSSettings, newSettings: TTSSettings) {
        // 检查具体哪些设置发生了变更
        if (oldSettings.languageCode != newSettings.languageCode) {
            onLanguageCodeChanged(oldSettings.languageCode, newSettings.languageCode)
        }
        
        if (oldSettings.voiceId != newSettings.voiceId) {
            onVoiceIdChanged(oldSettings.voiceId, newSettings.voiceId)
        }
        
        if (oldSettings.speechRate != newSettings.speechRate) {
            onSpeechRateChanged(oldSettings.speechRate, newSettings.speechRate)
        }
        
        if (oldSettings.pitch != newSettings.pitch) {
            onPitchChanged(oldSettings.pitch, newSettings.pitch)
        }
        
        if (oldSettings.volume != newSettings.volume) {
            onVolumeChanged(oldSettings.volume, newSettings.volume)
        }
    }
}