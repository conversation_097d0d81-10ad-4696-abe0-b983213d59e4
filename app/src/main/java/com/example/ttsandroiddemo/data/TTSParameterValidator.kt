package com.example.ttsandroiddemo.data

/**
 * TTS参数验证工具类
 */
object TTSParameterValidator {
    
    // 参数范围常量
    const val MIN_SPEECH_RATE = 0.1f
    const val MAX_SPEECH_RATE = 3.0f
    const val DEFAULT_SPEECH_RATE = 1.0f
    
    const val MIN_PITCH = 0.1f
    const val MAX_PITCH = 2.0f
    const val DEFAULT_PITCH = 1.0f
    
    const val MIN_VOLUME = 0.0f
    const val MAX_VOLUME = 1.0f
    const val DEFAULT_VOLUME = 1.0f
    
    /**
     * 验证并修正语速参数
     */
    fun validateSpeechRate(rate: Float): Float {
        return rate.coerceIn(MIN_SPEECH_RATE, MAX_SPEECH_RATE)
    }
    
    /**
     * 验证并修正音高参数
     */
    fun validatePitch(pitch: Float): Float {
        return pitch.coerceIn(MIN_PITCH, MAX_PITCH)
    }
    
    /**
     * 验证并修正音量参数
     */
    fun validateVolume(volume: Float): Float {
        return volume.coerceIn(MIN_VOLUME, MAX_VOLUME)
    }
    
    /**
     * 验证并修正所有TTS设置参数
     */
    fun validateSettings(settings: TTSSettings): TTSSettings {
        return settings.copy(
            speechRate = validateSpeechRate(settings.speechRate),
            pitch = validatePitch(settings.pitch),
            volume = validateVolume(settings.volume)
        )
    }
    
    /**
     * 检查语言代码格式是否有效
     */
    fun isValidLanguageCode(languageCode: String): Boolean {
        return languageCode.matches(Regex("^[a-z]{2}(-[A-Z]{2})?$"))
    }
}