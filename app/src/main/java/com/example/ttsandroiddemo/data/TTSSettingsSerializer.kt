package com.example.ttsandroiddemo.data

import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

/**
 * TTS设置序列化工具类
 */
object TTSSettingsSerializer {
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    /**
     * 将TTSSettings序列化为JSON字符串
     */
    fun serialize(settings: TTSSettings): String {
        return try {
            json.encodeToString(settings)
        } catch (e: Exception) {
            json.encodeToString(TTSSettings.getDefault())
        }
    }
    
    /**
     * 从JSON字符串反序列化TTSSettings
     */
    fun deserialize(jsonString: String): TTSSettings {
        return try {
            json.decodeFromString<TTSSettings>(jsonString)
        } catch (e: Exception) {
            TTSSettings.getDefault()
        }
    }
}