package com.example.ttsandroiddemo.data

import java.util.Locale

/**
 * 语言项数据类，用于表示可用的TTS语言
 */
data class LanguageItem(
    val locale: Locale,
    val displayName: String,
    val isAvailable: Boolean
) {
    companion object {
        /**
         * 从Locale创建LanguageItem
         */
        fun fromLocale(locale: Locale, isAvailable: Boolean = true): LanguageItem {
            return LanguageItem(
                locale = locale,
                displayName = "${locale.displayLanguage} (${locale.toLanguageTag()})",
                isAvailable = isAvailable
            )
        }
        
        /**
         * 获取常用语言列表
         */
        fun getCommonLanguages(): List<LanguageItem> {
            return listOf(
                fromLocale(Locale.forLanguageTag("zh-CN")),
                fromLocale(Locale.forLanguageTag("en-US")),
                fromLocale(Locale.forLanguageTag("ja-JP")),
                fromLocale(Locale.forLanguageTag("ko-KR")),
                fromLocale(Locale.forLanguageTag("fr-FR")),
                fromLocale(Locale.forLanguageTag("de-DE")),
                fromLocale(Locale.forLanguageTag("es-ES")),
                fromLocale(Locale.forLanguageTag("it-IT"))
            )
        }
    }
}