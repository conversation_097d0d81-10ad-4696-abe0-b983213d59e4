package com.example.ttsandroiddemo.data

import java.util.Locale

/**
 * TTS相关扩展函数
 */

/**
 * 为TTSSettings添加验证扩展函数
 */
fun TTSSettings.validated(): TTSSettings {
    return TTSParameterValidator.validateSettings(this)
}

/**
 * 为TTSSettings添加重置到默认值的扩展函数
 */
fun TTSSettings.resetToDefaults(): TTSSettings {
    return TTSSettings.getDefault()
}

/**
 * 为Locale添加显示名称扩展函数
 */
fun Locale.getDisplayNameWithCode(): String {
    return "${this.displayLanguage} (${this.toLanguageTag()})"
}

/**
 * 检查语言代码是否为中文
 */
fun String.isChineseLanguage(): Bo<PERSON><PERSON> {
    return this.startsWith("zh")
}

/**
 * 检查语言代码是否为英文
 */
fun String.isEnglishLanguage(): Bo<PERSON>an {
    return this.startsWith("en")
}