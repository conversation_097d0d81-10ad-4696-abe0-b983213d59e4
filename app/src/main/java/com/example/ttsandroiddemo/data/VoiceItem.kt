package com.example.ttsandroiddemo.data

import android.speech.tts.Voice

/**
 * 音色项数据类，用于表示可用的TTS音色
 */
data class VoiceItem(
    val voice: Voice,
    val displayName: String,
    val isNetworkRequired: Boolean
) {
    companion object {
        /**
         * 从Voice创建VoiceItem
         */
        fun fromVoice(voice: Voice): VoiceItem {
            return VoiceItem(
                voice = voice,
                displayName = voice.name,
                isNetworkRequired = voice.isNetworkConnectionRequired
            )
        }
        
        /**
         * 从Voice集合创建VoiceItem列表
         */
        fun fromVoiceSet(voices: Set<Voice>): List<VoiceItem> {
            return voices.map { fromVoice(it) }
                .sortedWith(compareBy<VoiceItem> { it.isNetworkRequired }.thenBy { it.displayName })
        }
    }
}