package com.example.ttsandroiddemo.viewmodel

import android.speech.tts.Voice
import java.util.Locale

/**
 * TTS界面操作密封类
 */
sealed class TTSUiAction {
    
    // 文本操作
    data class UpdateInputText(val text: String) : TTSUiAction()
    object ClearInputText : TTSUiAction()
    
    // 播放控制
    object PlayText : TTSUiAction()
    object StopPlaying : TTSUiAction()
    object PauseResume : TTSUiAction()
    
    // 语言和音色选择
    data class SelectLanguage(val locale: Locale) : TTSUiAction()
    data class SelectVoice(val voice: Voice?) : TTSUiAction()
    
    // 参数调整
    data class UpdateSpeechRate(val rate: Float) : TTSUiAction()
    data class UpdatePitch(val pitch: Float) : TTSUiAction()
    data class UpdateVolume(val volume: Float) : TTSUiAction()
    
    // 对话框控制
    data class ShowLanguageDialog(val show: Boolean) : TTSUiAction()
    data class ShowVoiceDialog(val show: Boolean) : TTSUiAction()
    data class ShowSettingsPanel(val show: Boolean) : TTSUiAction()
    
    // 消息处理
    object ClearErrorMessage : TTSUiAction()
    object ClearInfoMessage : TTSUiAction()
    object ClearAllMessages : TTSUiAction()
    
    // 设置操作
    object SaveSettings : TTSUiAction()
    object ResetToDefaults : TTSUiAction()
    object LoadSettings : TTSUiAction()
    
    // 系统操作
    object RetryInitialization : TTSUiAction()
    object RefreshAvailableOptions : TTSUiAction()
}

/**
 * TTS界面事件密封类
 */
sealed class TTSUiEvent {
    
    // 成功事件
    data class ShowMessage(val message: String) : TTSUiEvent()
    data class ShowError(val error: String) : TTSUiEvent()
    
    // 导航事件
    object NavigateToSettings : TTSUiEvent()
    object NavigateBack : TTSUiEvent()
    
    // 系统事件
    object RequestPermissions : TTSUiEvent()
    object OpenTTSSettings : TTSUiEvent()
    
    // 播放事件
    data class PlaybackStarted(val text: String) : TTSUiEvent()
    object PlaybackCompleted : TTSUiEvent()
    data class PlaybackError(val error: String) : TTSUiEvent()
}