package com.example.ttsandroiddemo.viewmodel

import android.speech.tts.Voice
import com.example.ttsandroiddemo.data.LanguageItem
import com.example.ttsandroiddemo.data.VoiceItem
import java.util.Locale

/**
 * TTS参数验证器，用于ViewModel层的参数验证
 */
object TTSParameterValidator {
    
    // 参数范围常量
    const val MIN_SPEECH_RATE = 0.1f
    const val MAX_SPEECH_RATE = 3.0f
    const val DEFAULT_SPEECH_RATE = 1.0f
    
    const val MIN_PITCH = 0.1f
    const val MAX_PITCH = 2.0f
    const val DEFAULT_PITCH = 1.0f
    
    const val MIN_VOLUME = 0.0f
    const val MAX_VOLUME = 1.0f
    const val DEFAULT_VOLUME = 1.0f
    
    const val MAX_TEXT_LENGTH = 5000
    
    /**
     * 验证文本输入
     */
    fun validateInputText(text: String): TextValidationResult {
        return when {
            text.isBlank() -> TextValidationResult.Empty
            text.length > MAX_TEXT_LENGTH -> TextValidationResult.TooLong(MAX_TEXT_LENGTH)
            text.trim().isEmpty() -> TextValidationResult.OnlyWhitespace
            else -> TextValidationResult.Valid
        }
    }
    
    /**
     * 验证语速参数
     */
    fun validateSpeechRate(rate: Float): ParameterValidationResult {
        return when {
            rate < MIN_SPEECH_RATE -> ParameterValidationResult.TooLow(MIN_SPEECH_RATE)
            rate > MAX_SPEECH_RATE -> ParameterValidationResult.TooHigh(MAX_SPEECH_RATE)
            else -> ParameterValidationResult.Valid(rate)
        }
    }
    
    /**
     * 验证音高参数
     */
    fun validatePitch(pitch: Float): ParameterValidationResult {
        return when {
            pitch < MIN_PITCH -> ParameterValidationResult.TooLow(MIN_PITCH)
            pitch > MAX_PITCH -> ParameterValidationResult.TooHigh(MAX_PITCH)
            else -> ParameterValidationResult.Valid(pitch)
        }
    }
    
    /**
     * 验证音量参数
     */
    fun validateVolume(volume: Float): ParameterValidationResult {
        return when {
            volume < MIN_VOLUME -> ParameterValidationResult.TooLow(MIN_VOLUME)
            volume > MAX_VOLUME -> ParameterValidationResult.TooHigh(MAX_VOLUME)
            else -> ParameterValidationResult.Valid(volume)
        }
    }
    
    /**
     * 验证语言是否在可用列表中
     */
    fun validateLanguage(locale: Locale, availableLanguages: List<LanguageItem>): Boolean {
        return availableLanguages.any { it.locale == locale && it.isAvailable }
    }
    
    /**
     * 验证音色是否在可用列表中
     */
    fun validateVoice(voice: Voice?, availableVoices: List<VoiceItem>): Boolean {
        return voice == null || availableVoices.any { it.voice == voice }
    }
    
    /**
     * 获取修正后的参数值
     */
    fun correctSpeechRate(rate: Float): Float = rate.coerceIn(MIN_SPEECH_RATE, MAX_SPEECH_RATE)
    fun correctPitch(pitch: Float): Float = pitch.coerceIn(MIN_PITCH, MAX_PITCH)
    fun correctVolume(volume: Float): Float = volume.coerceIn(MIN_VOLUME, MAX_VOLUME)
    
    /**
     * 检查参数是否为默认值
     */
    fun isDefaultSpeechRate(rate: Float): Boolean = rate == DEFAULT_SPEECH_RATE
    fun isDefaultPitch(pitch: Float): Boolean = pitch == DEFAULT_PITCH
    fun isDefaultVolume(volume: Float): Boolean = volume == DEFAULT_VOLUME
    
    /**
     * 获取参数的百分比表示
     */
    fun speechRateToPercentage(rate: Float): Int = ((rate - MIN_SPEECH_RATE) / (MAX_SPEECH_RATE - MIN_SPEECH_RATE) * 100).toInt()
    fun pitchToPercentage(pitch: Float): Int = ((pitch - MIN_PITCH) / (MAX_PITCH - MIN_PITCH) * 100).toInt()
    fun volumeToPercentage(volume: Float): Int = (volume * 100).toInt()
    
    /**
     * 从百分比转换为参数值
     */
    fun percentageToSpeechRate(percentage: Int): Float = MIN_SPEECH_RATE + (percentage / 100f) * (MAX_SPEECH_RATE - MIN_SPEECH_RATE)
    fun percentageToPitch(percentage: Int): Float = MIN_PITCH + (percentage / 100f) * (MAX_PITCH - MIN_PITCH)
    fun percentageToVolume(percentage: Int): Float = percentage / 100f
}

/**
 * 文本验证结果密封类
 */
sealed class TextValidationResult {
    object Valid : TextValidationResult()
    object Empty : TextValidationResult()
    object OnlyWhitespace : TextValidationResult()
    data class TooLong(val maxLength: Int) : TextValidationResult()
    
    val isValid: Boolean get() = this is Valid
    val errorMessage: String? get() = when (this) {
        is Valid -> null
        is Empty -> "请输入要朗读的文本"
        is OnlyWhitespace -> "文本不能只包含空格"
        is TooLong -> "文本长度不能超过 $maxLength 个字符"
    }
}

/**
 * 参数验证结果密封类
 */
sealed class ParameterValidationResult {
    data class Valid(val value: Float) : ParameterValidationResult()
    data class TooLow(val minValue: Float) : ParameterValidationResult()
    data class TooHigh(val maxValue: Float) : ParameterValidationResult()
    
    val isValid: Boolean get() = this is Valid
    val correctedValue: Float get() = when (this) {
        is Valid -> value
        is TooLow -> minValue
        is TooHigh -> maxValue
    }
    val errorMessage: String? get() = when (this) {
        is Valid -> null
        is TooLow -> "值不能小于 $minValue"
        is TooHigh -> "值不能大于 $maxValue"
    }
}