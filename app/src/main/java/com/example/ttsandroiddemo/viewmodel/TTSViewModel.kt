package com.example.ttsandroiddemo.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.ttsandroiddemo.data.LanguageItem
import com.example.ttsandroiddemo.data.TTSSettings
import com.example.ttsandroiddemo.data.VoiceItem
import com.example.ttsandroiddemo.storage.TTSSettingsRepository
import com.example.ttsandroiddemo.tts.TTSManager
import com.example.ttsandroiddemo.tts.ParameterRanges
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import java.util.Locale

/**
 * TTS界面ViewModel，管理UI状态和业务逻辑
 */
class TTSViewModel(application: Application) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "TTSViewModel"
    }
    
    // 核心组件
    private val ttsManager = TTSManager()
    private val settingsRepository = TTSSettingsRepository.getInstance(application)
    
    // UI状态
    private val _uiState = MutableStateFlow(TTSUiState.initial())
    val uiState: StateFlow<TTSUiState> = _uiState.asStateFlow()
    
    // 内部状态
    private var currentSettings = TTSSettings.getDefault()
    
    init {
        initializeTTS()
        observeSettings()
    }
    
    /**
     * 初始化TTS引擎
     */
    private fun initializeTTS() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isInitializing = true,
                isLoading = true
            )
            
            try {
                ttsManager.initialize(getApplication()) { success ->
                    viewModelScope.launch {
                        if (success) {
                            onTTSInitialized()
                        } else {
                            onTTSInitializationFailed()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception during TTS initialization", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "TTS初始化异常: ${e.message}",
                    isInitializing = false,
                    isLoading = false
                )
            }
        }
    }
    
    /**
     * TTS初始化成功处理
     */
    private suspend fun onTTSInitialized() {
        try {
            // 加载可用语言和音色
            val availableLanguages = ttsManager.getAvailableLanguageItems()
            val availableVoices = ttsManager.getAvailableVoiceItems()
            
            // 应用保存的设置
            applyCurrentSettings()
            
            _uiState.value = _uiState.value.copy(
                isInitialized = true,
                isInitializing = false,
                isLoading = false,
                availableLanguages = availableLanguages,
                availableVoices = availableVoices,
                errorMessage = null,
                isSettingsLoaded = true
            )
            
            Log.d(TAG, "TTS initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Exception in onTTSInitialized", e)
            _uiState.value = _uiState.value.copy(
                errorMessage = "TTS初始化后配置失败: ${e.message}",
                isInitializing = false,
                isLoading = false
            )
        }
    }
    
    /**
     * TTS初始化失败处理
     */
    private fun onTTSInitializationFailed() {
        _uiState.value = _uiState.value.copy(
            errorMessage = "TTS引擎初始化失败，请检查设备TTS设置",
            isInitialized = false,
            isInitializing = false,
            isLoading = false
        )
        Log.e(TAG, "TTS initialization failed")
    }
    
    /**
     * 观察设置变更
     */
    private fun observeSettings() {
        viewModelScope.launch {
            settingsRepository.settingsFlow.collect { settings ->
                currentSettings = settings
                updateUIFromSettings(settings)
                
                // 如果TTS已初始化，应用新设置
                if (_uiState.value.isInitialized) {
                    applyCurrentSettings()
                }
            }
        }
    }
    
    /**
     * 从设置更新UI状态
     */
    private fun updateUIFromSettings(settings: TTSSettings) {
        val locale = try {
            Locale.forLanguageTag(settings.languageCode)
        } catch (e: Exception) {
            Locale.getDefault()
        }
        
        _uiState.value = _uiState.value.copy(
            selectedLanguage = locale,
            speechRate = settings.speechRate,
            pitch = settings.pitch,
            volume = settings.volume
        )
    }
    
    /**
     * 应用当前设置到TTS引擎
     */
    private suspend fun applyCurrentSettings() {
        try {
            val locale = Locale.forLanguageTag(currentSettings.languageCode)
            
            // 设置语言
            if (!ttsManager.setLanguage(locale)) {
                Log.w(TAG, "Failed to set language: ${currentSettings.languageCode}")
            }
            
            // 设置音色（如果指定）
            currentSettings.voiceId?.let { voiceId ->
                val voice = ttsManager.getAvailableVoices().find { it.name == voiceId }
                if (voice != null) {
                    if (!ttsManager.setVoice(voice)) {
                        Log.w(TAG, "Failed to set voice: $voiceId")
                    }
                }
            }
            
            // 设置参数
            ttsManager.setSpeechRate(currentSettings.speechRate)
            ttsManager.setPitch(currentSettings.pitch)
            ttsManager.setVolume(currentSettings.volume)
            
            Log.d(TAG, "Settings applied to TTS engine")
        } catch (e: Exception) {
            Log.e(TAG, "Exception applying settings", e)
        }
    }
    
    /**
     * 更新输入文本
     */
    fun updateInputText(text: String) {
        val validationResult = TTSParameterValidator.validateInputText(text)
        _uiState.value = _uiState.value.copy(
            inputText = text,
            isTextValid = validationResult.isValid,
            hasUnsavedChanges = true,
            errorMessage = if (!validationResult.isValid) validationResult.errorMessage else null
        )
    }
    
    /**
     * 播放文本
     */
    fun playText() {
        val currentState = _uiState.value
        if (!currentState.canPlay) {
            return
        }
        
        try {
            ttsManager.speak(currentState.inputText)
            _uiState.value = _uiState.value.copy(
                errorMessage = null,
                infoMessage = "开始播放..."
            )
        } catch (e: Exception) {
            Log.e(TAG, "Exception during play", e)
            _uiState.value = _uiState.value.copy(
                errorMessage = "播放失败: ${e.message}"
            )
        }
    }
    
    /**
     * 停止播放
     */
    fun stopPlaying() {
        try {
            ttsManager.stop()
            _uiState.value = _uiState.value.copy(
                infoMessage = "播放已停止"
            )
        } catch (e: Exception) {
            Log.e(TAG, "Exception during stop", e)
            _uiState.value = _uiState.value.copy(
                errorMessage = "停止播放失败: ${e.message}"
            )
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 清除信息消息
     */
    fun clearInfoMessage() {
        _uiState.value = _uiState.value.copy(infoMessage = null)
    }
    
    /**
     * 显示/隐藏语言选择对话框
     */
    fun toggleLanguageDialog(show: Boolean = !_uiState.value.showLanguageDialog) {
        _uiState.value = _uiState.value.copy(showLanguageDialog = show)
    }
    
    /**
     * 显示/隐藏音色选择对话框
     */
    fun toggleVoiceDialog(show: Boolean = !_uiState.value.showVoiceDialog) {
        _uiState.value = _uiState.value.copy(showVoiceDialog = show)
    }
    
    /**
     * 显示/隐藏设置面板
     */
    fun toggleSettingsPanel(show: Boolean = !_uiState.value.showSettingsPanel) {
        _uiState.value = _uiState.value.copy(showSettingsPanel = show)
    }
    
    /**
     * 选择语言
     */
    fun selectLanguage(locale: Locale) {
        viewModelScope.launch {
            try {
                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    selectedLanguage = locale,
                    showLanguageDialog = false,
                    hasUnsavedChanges = true
                )
                
                // 应用到TTS引擎
                if (_uiState.value.isInitialized) {
                    val success = ttsManager.setLanguage(locale)
                    if (success) {
                        // 更新可用音色列表
                        val voicesForLanguage = ttsManager.getVoiceItemsForLanguage(locale)
                        _uiState.value = _uiState.value.copy(
                            availableVoices = voicesForLanguage,
                            selectedVoice = null // 重置音色选择
                        )
                        
                        // 保存到设置
                        settingsRepository.updateLanguageCode(locale.toLanguageTag())
                        
                        _uiState.value = _uiState.value.copy(
                            infoMessage = "语言已切换到: ${locale.displayLanguage}"
                        )
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "不支持的语言: ${locale.displayLanguage}"
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception selecting language", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "选择语言失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择音色
     */
    fun selectVoice(voice: android.speech.tts.Voice?) {
        viewModelScope.launch {
            try {
                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    selectedVoice = voice,
                    showVoiceDialog = false,
                    hasUnsavedChanges = true
                )
                
                // 应用到TTS引擎
                if (_uiState.value.isInitialized && voice != null) {
                    val success = ttsManager.setVoice(voice)
                    if (success) {
                        // 保存到设置
                        settingsRepository.updateVoiceId(voice.name)
                        
                        _uiState.value = _uiState.value.copy(
                            infoMessage = "音色已切换到: ${voice.name}"
                        )
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = "设置音色失败: ${voice.name}"
                        )
                    }
                } else if (voice == null) {
                    // 清除音色选择
                    settingsRepository.updateVoiceId(null)
                    _uiState.value = _uiState.value.copy(
                        infoMessage = "已清除音色选择"
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception selecting voice", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "选择音色失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新语速
     */
    fun updateSpeechRate(rate: Float) {
        viewModelScope.launch {
            try {
                val validationResult = TTSParameterValidator.validateSpeechRate(rate)
                val validatedRate = validationResult.correctedValue
                
                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    speechRate = validatedRate,
                    hasUnsavedChanges = true,
                    errorMessage = if (!validationResult.isValid) validationResult.errorMessage else null
                )
                
                // 应用到TTS引擎
                if (_uiState.value.isInitialized) {
                    ttsManager.setSpeechRate(validatedRate)
                    
                    // 保存到设置
                    settingsRepository.updateSpeechRate(validatedRate)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception updating speech rate", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "更新语速失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新音高
     */
    fun updatePitch(pitch: Float) {
        viewModelScope.launch {
            try {
                val validationResult = TTSParameterValidator.validatePitch(pitch)
                val validatedPitch = validationResult.correctedValue
                
                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    pitch = validatedPitch,
                    hasUnsavedChanges = true,
                    errorMessage = if (!validationResult.isValid) validationResult.errorMessage else null
                )
                
                // 应用到TTS引擎
                if (_uiState.value.isInitialized) {
                    ttsManager.setPitch(validatedPitch)
                    
                    // 保存到设置
                    settingsRepository.updatePitch(validatedPitch)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception updating pitch", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "更新音高失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新音量
     */
    fun updateVolume(volume: Float) {
        viewModelScope.launch {
            try {
                val validationResult = TTSParameterValidator.validateVolume(volume)
                val validatedVolume = validationResult.correctedValue
                
                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    volume = validatedVolume,
                    hasUnsavedChanges = true,
                    errorMessage = if (!validationResult.isValid) validationResult.errorMessage else null
                )
                
                // 应用到TTS引擎
                if (_uiState.value.isInitialized) {
                    ttsManager.setVolume(validatedVolume)
                    
                    // 保存到设置
                    settingsRepository.updateVolume(validatedVolume)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception updating volume", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "更新音量失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 批量更新参数
     */
    fun updateParameters(
        speechRate: Float? = null,
        pitch: Float? = null,
        volume: Float? = null
    ) {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                val newSpeechRate = speechRate?.coerceIn(0.1f, 3.0f) ?: currentState.speechRate
                val newPitch = pitch?.coerceIn(0.1f, 2.0f) ?: currentState.pitch
                val newVolume = volume?.coerceIn(0.0f, 1.0f) ?: currentState.volume
                
                // 更新UI状态
                _uiState.value = currentState.copy(
                    speechRate = newSpeechRate,
                    pitch = newPitch,
                    volume = newVolume,
                    hasUnsavedChanges = true
                )
                
                // 应用到TTS引擎
                if (currentState.isInitialized) {
                    speechRate?.let { ttsManager.setSpeechRate(newSpeechRate) }
                    pitch?.let { ttsManager.setPitch(newPitch) }
                    volume?.let { ttsManager.setVolume(newVolume) }
                    
                    // 批量保存到设置
                    settingsRepository.updateSettings(
                        speechRate = if (speechRate != null) newSpeechRate else null,
                        pitch = if (pitch != null) newPitch else null,
                        volume = if (volume != null) newVolume else null
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception updating parameters", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "更新参数失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 重置参数到默认值
     */
    fun resetParametersToDefaults() {
        viewModelScope.launch {
            try {
                updateParameters(
                    speechRate = 1.0f,
                    pitch = 1.0f,
                    volume = 1.0f
                )
                
                _uiState.value = _uiState.value.copy(
                    infoMessage = "参数已重置为默认值"
                )
            } catch (e: Exception) {
                Log.e(TAG, "Exception resetting parameters", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "重置参数失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 保存当前设置
     */
    fun saveCurrentSettings() {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                val settings = TTSSettings(
                    languageCode = currentState.selectedLanguage.toLanguageTag(),
                    voiceId = currentState.selectedVoice?.name,
                    speechRate = currentState.speechRate,
                    pitch = currentState.pitch,
                    volume = currentState.volume
                )
                
                settingsRepository.saveSettings(settings)
                
                _uiState.value = _uiState.value.copy(
                    hasUnsavedChanges = false,
                    infoMessage = "设置已保存"
                )
            } catch (e: Exception) {
                Log.e(TAG, "Exception saving settings", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "保存设置失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 重置所有设置到默认值
     */
    fun resetAllSettingsToDefaults() {
        viewModelScope.launch {
            try {
                settingsRepository.resetToDefaults()
                
                _uiState.value = _uiState.value.copy(
                    hasUnsavedChanges = false,
                    infoMessage = "所有设置已重置为默认值"
                )
            } catch (e: Exception) {
                Log.e(TAG, "Exception resetting all settings", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "重置设置失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 刷新可用选项（语言和音色）
     */
    fun refreshAvailableOptions() {
        viewModelScope.launch {
            try {
                if (_uiState.value.isInitialized) {
                    val availableLanguages = ttsManager.getAvailableLanguageItems()
                    val availableVoices = ttsManager.getVoiceItemsForLanguage(_uiState.value.selectedLanguage)
                    
                    _uiState.value = _uiState.value.copy(
                        availableLanguages = availableLanguages,
                        availableVoices = availableVoices,
                        infoMessage = "可用选项已刷新"
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception refreshing options", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "刷新选项失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 获取参数范围信息
     */
    fun getParameterRanges(): ParameterRanges {
        return ParameterRanges(
            speechRateRange = 0.1f to 3.0f,
            pitchRange = 0.1f to 2.0f,
            volumeRange = 0.0f to 1.0f
        )
    }
    
    /**
     * 验证当前参数是否在有效范围内
     */
    fun validateCurrentParameters(): Boolean {
        val currentState = _uiState.value
        val ranges = getParameterRanges()
        
        return currentState.speechRate in ranges.speechRateRange.first..ranges.speechRateRange.second &&
               currentState.pitch in ranges.pitchRange.first..ranges.pitchRange.second &&
               currentState.volume in ranges.volumeRange.first..ranges.volumeRange.second
    }
    
    /**
     * 观察TTS状态变化
     */
    private fun observeTTSState() {
        viewModelScope.launch {
            combine(
                ttsManager.isInitialized,
                ttsManager.isPlaying,
                ttsManager.currentError
            ) { isInitialized, isPlaying, error ->
                Triple(isInitialized, isPlaying, error)
            }.collect { (isInitialized, isPlaying, error) ->
                _uiState.value = _uiState.value.copy(
                    isInitialized = isInitialized,
                    isPlaying = isPlaying,
                    errorMessage = error
                )
            }
        }
    }
    
    /**
     * 重试TTS初始化
     */
    fun retryInitialization() {
        initializeTTS()
    }
    
    override fun onCleared() {
        super.onCleared()
        try {
            ttsManager.release()
            settingsRepository.release()
            Log.d(TAG, "ViewModel cleared and resources released")
        } catch (e: Exception) {
            Log.e(TAG, "Exception during cleanup", e)
        }
    }
}