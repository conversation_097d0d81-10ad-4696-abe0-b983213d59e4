package com.example.ttsandroiddemo.viewmodel

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

/**
 * TTS ViewModel工厂类
 */
class TTSViewModelFactory(
    private val application: Application
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(TTSViewModel::class.java)) {
            return TTSViewModel(application) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
}

/**
 * 扩展函数，简化ViewModel创建
 */
fun Application.createTTSViewModelFactory(): TTSViewModelFactory {
    return TTSViewModelFactory(this)
}