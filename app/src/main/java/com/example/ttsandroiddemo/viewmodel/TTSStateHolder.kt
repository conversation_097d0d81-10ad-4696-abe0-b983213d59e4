package com.example.ttsandroiddemo.viewmodel

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.lifecycle.viewmodel.compose.viewModel

/**
 * TTS状态持有者，用于Compose UI状态管理
 */
@Stable
class TTSStateHolder(
    private val viewModel: TTSViewModel
) {
    
    val uiState by viewModel.uiState.collectAsState()
    
    // 文本操作
    fun updateInputText(text: String) = viewModel.updateInputText(text)
    fun clearInputText() = viewModel.updateInputText("")
    
    // 播放控制
    fun playText() = viewModel.playText()
    fun stopPlaying() = viewModel.stopPlaying()
    
    // 对话框控制
    fun showLanguageDialog(show: Boolean = true) = viewModel.toggleLanguageDialog(show)
    fun hideLanguageDialog() = viewModel.toggleLanguageDialog(false)
    fun showVoiceDialog(show: Boolean = true) = viewModel.toggleVoiceDialog(show)
    fun hideVoiceDialog() = viewModel.toggleVoiceDialog(false)
    fun toggleSettingsPanel() = viewModel.toggleSettingsPanel()
    
    // 消息处理
    fun clearErrorMessage() = viewModel.clearErrorMessage()
    fun clearInfoMessage() = viewModel.clearInfoMessage()
    fun clearAllMessages() {
        viewModel.clearErrorMessage()
        viewModel.clearInfoMessage()
    }
    
    // 参数管理
    fun selectLanguage(locale: java.util.Locale) = viewModel.selectLanguage(locale)
    fun selectVoice(voice: android.speech.tts.Voice?) = viewModel.selectVoice(voice)
    fun updateSpeechRate(rate: Float) = viewModel.updateSpeechRate(rate)
    fun updatePitch(pitch: Float) = viewModel.updatePitch(pitch)
    fun updateVolume(volume: Float) = viewModel.updateVolume(volume)
    fun updateParameters(speechRate: Float? = null, pitch: Float? = null, volume: Float? = null) = 
        viewModel.updateParameters(speechRate, pitch, volume)
    
    // 设置管理
    fun saveCurrentSettings() = viewModel.saveCurrentSettings()
    fun resetParametersToDefaults() = viewModel.resetParametersToDefaults()
    fun resetAllSettingsToDefaults() = viewModel.resetAllSettingsToDefaults()
    fun refreshAvailableOptions() = viewModel.refreshAvailableOptions()
    
    // 系统操作
    fun retryInitialization() = viewModel.retryInitialization()
    
    // 便利属性
    val canPlay: Boolean get() = uiState.canPlay
    val canStop: Boolean get() = uiState.canStop
    val hasError: Boolean get() = uiState.hasError
    val hasInfo: Boolean get() = uiState.hasInfo
    val isReady: Boolean get() = uiState.isReady
    val isLoading: Boolean get() = uiState.isLoading
    val hasUnsavedChanges: Boolean get() = uiState.hasUnsavedChanges
    val parameterRanges get() = viewModel.getParameterRanges()
    val isParametersValid: Boolean get() = viewModel.validateCurrentParameters()
}

/**
 * Compose函数，创建并记住TTSStateHolder
 */
@Composable
fun rememberTTSStateHolder(
    viewModel: TTSViewModel = viewModel()
): TTSStateHolder {
    return remember(viewModel) {
        TTSStateHolder(viewModel)
    }
}