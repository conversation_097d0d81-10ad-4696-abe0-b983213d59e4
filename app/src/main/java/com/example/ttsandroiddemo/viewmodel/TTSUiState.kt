package com.example.ttsandroiddemo.viewmodel

import android.speech.tts.Voice
import com.example.ttsandroiddemo.data.LanguageItem
import com.example.ttsandroiddemo.data.VoiceItem
import java.util.Locale

/**
 * TTS界面状态数据类
 */
data class TTSUiState(
    // 文本输入状态
    val inputText: String = "",
    val isTextValid: Boolean = true,
    
    // TTS引擎状态
    val isInitialized: Boolean = false,
    val isInitializing: Boolean = false,
    val isPlaying: Boolean = false,
    
    // 语言和音色
    val selectedLanguage: Locale = Locale.getDefault(),
    val selectedVoice: Voice? = null,
    val availableLanguages: List<LanguageItem> = emptyList(),
    val availableVoices: List<VoiceItem> = emptyList(),
    
    // TTS参数
    val speechRate: Float = 1.0f,
    val pitch: Float = 1.0f,
    val volume: Float = 1.0f,
    
    // 错误和消息状态
    val errorMessage: String? = null,
    val infoMessage: String? = null,
    val isLoading: Boolean = false,
    
    // UI状态
    val showLanguageDialog: Boolean = false,
    val showVoiceDialog: Boolean = false,
    val showSettingsPanel: Boolean = true,
    
    // 设置状态
    val hasUnsavedChanges: Boolean = false,
    val isSettingsLoaded: Boolean = false
) {
    /**
     * 检查是否可以播放
     */
    val canPlay: Boolean
        get() = isInitialized && !isPlaying && inputText.isNotBlank() && isTextValid
    
    /**
     * 检查是否可以停止
     */
    val canStop: Boolean
        get() = isInitialized && isPlaying
    
    /**
     * 检查是否有错误
     */
    val hasError: Boolean
        get() = errorMessage != null
    
    /**
     * 检查是否有信息消息
     */
    val hasInfo: Boolean
        get() = infoMessage != null
    
    /**
     * 检查TTS是否准备就绪
     */
    val isReady: Boolean
        get() = isInitialized && !isInitializing && !isLoading
    
    /**
     * 获取当前语言的显示名称
     */
    val selectedLanguageDisplayName: String
        get() = availableLanguages.find { it.locale == selectedLanguage }?.displayName 
            ?: selectedLanguage.displayName
    
    /**
     * 获取当前音色的显示名称
     */
    val selectedVoiceDisplayName: String?
        get() = selectedVoice?.name
    
    companion object {
        /**
         * 创建初始状态
         */
        fun initial() = TTSUiState()
        
        /**
         * 创建加载状态
         */
        fun loading() = TTSUiState(
            isLoading = true,
            isInitializing = true
        )
        
        /**
         * 创建错误状态
         */
        fun error(message: String) = TTSUiState(
            errorMessage = message,
            isLoading = false,
            isInitializing = false
        )
        
        /**
         * 创建就绪状态
         */
        fun ready() = TTSUiState(
            isInitialized = true,
            isInitializing = false,
            isLoading = false,
            isSettingsLoaded = true
        )
    }
}