# Technology Stack

## Build System
- **Gradle** with <PERSON><PERSON><PERSON> DSL (build.gradle.kts)
- **Android Gradle Plugin** 8.12.0-rc01
- **Kotlin** 2.0.21

## Core Technologies
- **Android SDK**: Target API 36, Min API 24
- **Kotlin**: Primary language with JVM target 11
- **Jetpack Compose**: Modern UI toolkit
- **Material Design 3**: UI components and theming

## Key Dependencies
- AndroidX Core KTX
- Lifecycle Runtime KTX
- Activity Compose
- Compose BOM (2024.09.00)
- Material3 for Compose

## Testing Framework
- **Unit Tests**: JUnit 4.13.2
- **Instrumented Tests**: AndroidX Test (JUnit, Espresso)
- **Compose Tests**: UI Test JUnit4

## Common Commands

### Build & Run
```bash
./gradlew build                 # Build the project
./gradlew assembleDebug        # Build debug APK
./gradlew assembleRelease      # Build release APK
./gradlew installDebug         # Install debug APK to device
```

### Testing
```bash
./gradlew test                 # Run unit tests
./gradlew connectedAndroidTest # Run instrumented tests
./gradlew testDebugUnitTest    # Run debug unit tests
```

### Clean & Sync
```bash
./gradlew clean               # Clean build artifacts
./gradlew --refresh-dependencies # Refresh dependencies
```

## Development Setup
- Java 11 compatibility
- Compose compiler enabled
- ProGuard disabled in debug builds
- Edge-to-edge display support enabled