# Project Structure

## Root Level
- **build.gradle.kts**: Root build configuration
- **settings.gradle.kts**: Project settings and module inclusion
- **gradle/**: Gradle wrapper and version catalog (libs.versions.toml)
- **local.properties**: Local SDK paths (not in version control)

## App Module (`app/`)
Standard Android app module structure following Android conventions.

### Source Code (`app/src/`)
```
app/src/
├── main/                           # Main source set
│   ├── java/com/example/ttsandroiddemo/
│   │   ├── MainActivity.kt         # Main activity with Compose setup
│   │   └── ui/theme/              # Compose theme definitions
│   │       ├── Color.kt           # Color palette
│   │       ├── Theme.kt           # App theme
│   │       └── Type.kt            # Typography
│   ├── res/                       # Android resources
│   │   ├── drawable/              # Vector drawables and icons
│   │   ├── mipmap-*/              # App launcher icons (various densities)
│   │   ├── values/                # Strings, colors, themes
│   │   └── xml/                   # Backup and data extraction rules
│   └── AndroidManifest.xml        # App manifest
├── test/                          # Unit tests
└── androidTest/                   # Instrumented tests
```

## Package Structure
- **Base package**: `com.example.ttsandroiddemo`
- **UI package**: `com.example.ttsandroiddemo.ui.theme`

## Naming Conventions
- **Activities**: PascalCase ending with "Activity" (MainActivity)
- **Composables**: PascalCase functions (Greeting, GreetingPreview)
- **Resources**: snake_case (ic_launcher_background)
- **Packages**: lowercase with dots

## Key Files
- **MainActivity.kt**: Entry point with Compose setup and edge-to-edge support
- **Theme files**: Centralized theming for consistent UI
- **AndroidManifest.xml**: App configuration and permissions
- **build.gradle.kts**: Module dependencies and Android configuration

## Resource Organization
- Launcher icons in multiple densities (mipmap-*)
- Vector drawables for scalable graphics
- Centralized strings and themes in values/
- Backup and data extraction rules in xml/