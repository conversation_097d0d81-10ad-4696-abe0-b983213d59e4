# Product Overview

TTSAndroidDemo is an Android application demo project focused on Text-to-Speech (TTS) functionality. The app serves as a demonstration or learning project for implementing TTS features in Android using modern development practices.

## Key Features
- Text-to-Speech functionality (intended)
- Modern Android UI with Jetpack Compose
- Material Design 3 implementation

## Target Platform
- Android devices (API 24+)
- Supports modern Android versions up to API 36