# 设计文档

## 概述

TTS界面功能将为现有的Android应用程序添加一个全面的文本转语音界面。该设计基于Android的内置TextToSpeech API，使用Jetpack Compose构建现代化的用户界面，并遵循Material Design 3设计原则。

该功能将作为主界面的一部分集成到现有的MainActivity中，替换当前的简单问候界面，提供完整的TTS功能体验。

## 架构

### 整体架构模式
- **MVVM (Model-View-ViewModel)**: 使用Compose状态管理和ViewModel来分离UI逻辑和业务逻辑
- **单一Activity架构**: 在现有MainActivity中实现TTS功能
- **组合式UI**: 使用Jetpack Compose构建响应式用户界面

### 核心组件层次
```
MainActivity
├── TTSScreen (主界面Composable)
├── TTSViewModel (状态管理和业务逻辑)
├── TTSManager (TTS引擎封装)
└── TTSSettings (设置数据类)
```

## 组件和接口

### 1. TTSManager (TTS引擎管理器)
负责封装Android TextToSpeech API的核心功能。

**主要职责:**
- 初始化和管理TextToSpeech实例
- 处理TTS引擎的生命周期
- 提供语言、音色、参数设置接口
- 管理播放状态和错误处理

**关键方法:**
```kotlin
class TTSManager {
    fun initialize(context: Context, onInitComplete: (Boolean) -> Unit)
    fun speak(text: String)
    fun stop()
    fun setLanguage(locale: Locale): Boolean
    fun setVoice(voice: Voice): Boolean
    fun setSpeechRate(rate: Float)
    fun setPitch(pitch: Float)
    fun getAvailableLanguages(): Set<Locale>
    fun getAvailableVoices(): Set<Voice>
    fun release()
}
```

### 2. TTSViewModel (状态管理)
管理UI状态和业务逻辑，作为View和Model之间的桥梁。

**状态管理:**
```kotlin
data class TTSUiState(
    val inputText: String = "",
    val isPlaying: Boolean = false,
    val isInitialized: Boolean = false,
    val selectedLanguage: Locale = Locale.getDefault(),
    val selectedVoice: Voice? = null,
    val speechRate: Float = 1.0f,
    val pitch: Float = 1.0f,
    val volume: Float = 1.0f,
    val availableLanguages: List<Locale> = emptyList(),
    val availableVoices: List<Voice> = emptyList(),
    val errorMessage: String? = null
)
```

### 3. TTSScreen (主界面组合函数)
使用Jetpack Compose构建的主要用户界面。

**UI组件结构:**
```
TTSScreen
├── TextInputSection (文本输入区域)
├── PlayControlSection (播放控制区域)
└── SettingsSection (参数设置区域)
    ├── LanguageSelector (语言选择)
    ├── VoiceSelector (音色选择)
    ├── SpeechRateSlider (语速滑块)
    ├── PitchSlider (音高滑块)
    └── VolumeSlider (音量滑块)
```

### 4. 设置持久化
使用SharedPreferences存储用户偏好设置。

**存储的设置:**
- 选择的语言代码
- 选择的音色ID
- 语速值
- 音高值
- 音量值

## 数据模型

### TTSSettings (设置数据类)
```kotlin
data class TTSSettings(
    val languageCode: String = "zh-CN",
    val voiceId: String? = null,
    val speechRate: Float = 1.0f,
    val pitch: Float = 1.0f,
    val volume: Float = 1.0f
)
```

### 语言和音色模型
```kotlin
data class LanguageItem(
    val locale: Locale,
    val displayName: String,
    val isAvailable: Boolean
)

data class VoiceItem(
    val voice: Voice,
    val displayName: String,
    val isNetworkRequired: Boolean
)
```

## 错误处理

### TTS初始化错误
- **场景**: TTS引擎初始化失败
- **处理**: 显示错误消息，禁用播放功能，提供重试选项

### 语言不支持错误
- **场景**: 选择的语言在设备上不可用
- **处理**: 显示警告消息，自动回退到默认语言，更新UI状态

### 播放错误
- **场景**: TTS播放过程中出现错误
- **处理**: 停止播放，显示错误消息，重置播放状态

### 权限错误
- **场景**: 音频权限不足
- **处理**: 请求必要权限，显示权限说明

## 测试策略

### 单元测试
- **TTSManager**: 测试TTS引擎的初始化、参数设置、播放控制
- **TTSViewModel**: 测试状态管理、用户交互处理、错误处理
- **设置持久化**: 测试设置的保存和加载

### UI测试
- **文本输入**: 测试文本输入和验证
- **播放控制**: 测试播放按钮的状态变化
- **参数调整**: 测试滑块和选择器的交互
- **错误显示**: 测试错误消息的显示

### 集成测试
- **完整流程**: 测试从文本输入到语音播放的完整流程
- **设置持久化**: 测试应用重启后设置的恢复
- **多语言支持**: 测试不同语言的切换和播放

### 设备兼容性测试
- **不同Android版本**: 测试API 24+的兼容性
- **不同TTS引擎**: 测试Google TTS、系统TTS等不同引擎
- **不同设备**: 测试不同制造商设备的兼容性

## UI/UX设计原则

### Material Design 3
- 使用Material 3组件和主题
- 遵循Material Design的颜色、字体、间距规范
- 支持动态颜色和深色模式

### 可访问性
- 提供内容描述和语义标签
- 支持TalkBack和其他辅助功能
- 确保足够的颜色对比度
- 支持大字体和缩放

### 响应式设计
- 适配不同屏幕尺寸和方向
- 使用灵活的布局和约束
- 优化平板电脑和大屏设备的体验

### 用户反馈
- 提供即时的视觉反馈
- 使用适当的加载指示器
- 清晰的错误消息和状态提示
- 平滑的动画和过渡效果

## 性能考虑

### TTS引擎优化
- 延迟初始化TTS引擎
- 合理管理TTS资源的生命周期
- 避免频繁的参数设置调用

### UI性能
- 使用Compose的状态优化
- 避免不必要的重组
- 合理使用remember和derivedStateOf

### 内存管理
- 及时释放TTS资源
- 避免内存泄漏
- 合理管理大量语言和音色数据

## 安全和隐私

### 数据隐私
- 输入的文本仅在本地处理
- 不向外部服务发送用户文本
- 设置数据仅存储在本地

### 权限管理
- 仅请求必要的音频权限
- 清晰说明权限用途
- 优雅处理权限拒绝情况